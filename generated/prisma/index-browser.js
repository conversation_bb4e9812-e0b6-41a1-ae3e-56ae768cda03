
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('./runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.10.1
 * Query Engine version: 9b628578b3b7cae625e8c927178f15a170e74a9c
 */
Prisma.prismaVersion = {
  client: "6.10.1",
  engine: "9b628578b3b7cae625e8c927178f15a170e74a9c"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  authUserId: 'authUserId',
  username: 'username',
  displayName: 'displayName',
  email: 'email',
  role: 'role',
  status: 'status',
  technicalLevel: 'technicalLevel',
  profilePictureUrl: 'profilePictureUrl',
  bio: 'bio',
  socialLinks: 'socialLinks',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  lastLogin: 'lastLogin'
};

exports.Prisma.EntityTypeScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  slug: 'slug',
  iconUrl: 'iconUrl',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EntityScalarFieldEnum = {
  id: 'id',
  name: 'name',
  websiteUrl: 'websiteUrl',
  entityTypeId: 'entityTypeId',
  shortDescription: 'shortDescription',
  description: 'description',
  logoUrl: 'logoUrl',
  documentationUrl: 'documentationUrl',
  contactUrl: 'contactUrl',
  privacyPolicyUrl: 'privacyPolicyUrl',
  foundedYear: 'foundedYear',
  status: 'status',
  socialLinks: 'socialLinks',
  submitterId: 'submitterId',
  legacyId: 'legacyId',
  reviewCount: 'reviewCount',
  avgRating: 'avgRating',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  metaTitle: 'metaTitle',
  metaDescription: 'metaDescription',
  scrapedReviewSentimentLabel: 'scrapedReviewSentimentLabel',
  scrapedReviewSentimentScore: 'scrapedReviewSentimentScore',
  scrapedReviewCount: 'scrapedReviewCount',
  employeeCountRange: 'employeeCountRange',
  fundingStage: 'fundingStage',
  locationSummary: 'locationSummary',
  refLink: 'refLink',
  affiliateStatus: 'affiliateStatus'
};

exports.Prisma.CategoryScalarFieldEnum = {
  id: 'id',
  name: 'name',
  slug: 'slug',
  description: 'description',
  iconUrl: 'iconUrl',
  parentId: 'parentId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TagScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  slug: 'slug',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EntityTagScalarFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  tagId: 'tagId',
  assignedBy: 'assignedBy',
  createdAt: 'createdAt'
};

exports.Prisma.EntityCategoryScalarFieldEnum = {
  entityId: 'entityId',
  categoryId: 'categoryId',
  assignedAt: 'assignedAt',
  assignedBy: 'assignedBy'
};

exports.Prisma.ReviewScalarFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  userId: 'userId',
  rating: 'rating',
  title: 'title',
  content: 'content',
  status: 'status',
  moderatorId: 'moderatorId',
  moderationNotes: 'moderationNotes',
  upvotes: 'upvotes',
  downvotes: 'downvotes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ReviewVoteScalarFieldEnum = {
  id: 'id',
  reviewId: 'reviewId',
  userId: 'userId',
  isUpvote: 'isUpvote',
  createdAt: 'createdAt'
};

exports.Prisma.EntityDetailsToolScalarFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  technicalLevel: 'technicalLevel',
  hasApi: 'hasApi',
  hasFreeTier: 'hasFreeTier',
  keyFeatures: 'keyFeatures',
  useCases: 'useCases',
  learningCurve: 'learningCurve',
  pricingModel: 'pricingModel',
  priceRange: 'priceRange',
  integrations: 'integrations',
  platforms: 'platforms',
  programmingLanguages: 'programmingLanguages',
  frameworks: 'frameworks',
  libraries: 'libraries',
  targetAudience: 'targetAudience',
  deploymentOptions: 'deploymentOptions',
  supportedOs: 'supportedOs',
  mobileSupport: 'mobileSupport',
  apiAccess: 'apiAccess',
  customizationLevel: 'customizationLevel',
  trialAvailable: 'trialAvailable',
  demoAvailable: 'demoAvailable',
  openSource: 'openSource',
  supportChannels: 'supportChannels',
  pricingDetails: 'pricingDetails',
  pricingUrl: 'pricingUrl',
  supportEmail: 'supportEmail',
  hasLiveChat: 'hasLiveChat',
  communityUrl: 'communityUrl'
};

exports.Prisma.EntityDetailsAgencyScalarFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  servicesOffered: 'servicesOffered',
  industryFocus: 'industryFocus',
  targetClientSize: 'targetClientSize',
  portfolioUrl: 'portfolioUrl',
  pricingInfo: 'pricingInfo',
  targetAudience: 'targetAudience',
  locationSummary: 'locationSummary'
};

exports.Prisma.EntityDetailsContentCreatorScalarFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  creatorName: 'creatorName',
  primaryPlatform: 'primaryPlatform',
  focusAreas: 'focusAreas',
  followerCount: 'followerCount',
  exampleContentUrl: 'exampleContentUrl'
};

exports.Prisma.EntityDetailsCommunityScalarFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  platform: 'platform',
  memberCount: 'memberCount',
  focusTopics: 'focusTopics',
  rulesUrl: 'rulesUrl',
  inviteUrl: 'inviteUrl',
  mainChannelUrl: 'mainChannelUrl'
};

exports.Prisma.EntityDetailsNewsletterScalarFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  frequency: 'frequency',
  mainTopics: 'mainTopics',
  archiveUrl: 'archiveUrl',
  subscribeUrl: 'subscribeUrl',
  authorName: 'authorName',
  subscriberCount: 'subscriberCount'
};

exports.Prisma.EntityDetailsCourseScalarFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  instructorName: 'instructorName',
  durationText: 'durationText',
  skillLevel: 'skillLevel',
  prerequisites: 'prerequisites',
  syllabusUrl: 'syllabusUrl',
  enrollmentCount: 'enrollmentCount',
  certificateAvailable: 'certificateAvailable'
};

exports.Prisma.UserSavedEntityScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  entityId: 'entityId',
  createdAt: 'createdAt'
};

exports.Prisma.UserFollowedTagScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  tagId: 'tagId',
  createdAt: 'createdAt'
};

exports.Prisma.UserFollowedCategoryScalarFieldEnum = {
  userId: 'userId',
  categoryId: 'categoryId',
  followedAt: 'followedAt'
};

exports.Prisma.UserActivityLogScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  actionType: 'actionType',
  entityId: 'entityId',
  categoryId: 'categoryId',
  tagId: 'tagId',
  reviewId: 'reviewId',
  targetUserId: 'targetUserId',
  details: 'details',
  createdAt: 'createdAt'
};

exports.Prisma.UserNotificationSettingsScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  emailOnNewReview: 'emailOnNewReview',
  emailOnReviewResponse: 'emailOnReviewResponse',
  emailOnNewFollower: 'emailOnNewFollower',
  emailOnNewEntityInFollowed: 'emailOnNewEntityInFollowed',
  emailNewsletter: 'emailNewsletter',
  emailMarketing: 'emailMarketing',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.BadgeTypeScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  iconUrl: 'iconUrl',
  scope: 'scope',
  criteria: 'criteria',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UserBadgeScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  badgeTypeId: 'badgeTypeId',
  grantedAt: 'grantedAt',
  grantedBy: 'grantedBy',
  notes: 'notes'
};

exports.Prisma.EntityBadgeScalarFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  badgeTypeId: 'badgeTypeId',
  grantedAt: 'grantedAt',
  grantedBy: 'grantedBy',
  expiresAt: 'expiresAt',
  notes: 'notes'
};

exports.Prisma.EntityDetailsDatasetScalarFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  format: 'format',
  sourceUrl: 'sourceUrl',
  license: 'license',
  sizeInBytes: 'sizeInBytes',
  description: 'description',
  accessNotes: 'accessNotes',
  collectionMethod: 'collectionMethod',
  updateFrequency: 'updateFrequency',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EntityDetailsResearchPaperScalarFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  publicationDate: 'publicationDate',
  doi: 'doi',
  authors: 'authors',
  journalOrConference: 'journalOrConference',
  citationCount: 'citationCount',
  abstract: 'abstract',
  pdfUrl: 'pdfUrl',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EntityDetailsSoftwareScalarFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  currentVersion: 'currentVersion',
  licenseType: 'licenseType',
  hasApi: 'hasApi',
  hasFreeTier: 'hasFreeTier',
  keyFeatures: 'keyFeatures',
  useCases: 'useCases',
  pricingModel: 'pricingModel',
  priceRange: 'priceRange',
  integrations: 'integrations',
  platformCompatibility: 'platformCompatibility',
  programmingLanguages: 'programmingLanguages',
  frameworks: 'frameworks',
  libraries: 'libraries',
  targetAudience: 'targetAudience',
  deploymentOptions: 'deploymentOptions',
  supportedOs: 'supportedOs',
  mobileSupport: 'mobileSupport',
  apiAccess: 'apiAccess',
  customizationLevel: 'customizationLevel',
  trialAvailable: 'trialAvailable',
  demoAvailable: 'demoAvailable',
  openSource: 'openSource',
  supportChannels: 'supportChannels',
  pricingDetails: 'pricingDetails',
  pricingUrl: 'pricingUrl',
  supportEmail: 'supportEmail',
  hasLiveChat: 'hasLiveChat',
  communityUrl: 'communityUrl',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EntityDetailsModelScalarFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  license: 'license',
  modelArchitecture: 'modelArchitecture',
  trainingDataset: 'trainingDataset',
  performanceMetrics: 'performanceMetrics',
  useCases: 'useCases',
  frameworks: 'frameworks',
  libraries: 'libraries',
  targetAudience: 'targetAudience',
  deploymentOptions: 'deploymentOptions',
  inputDataTypes: 'inputDataTypes',
  outputDataTypes: 'outputDataTypes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EntityDetailsProjectReferenceScalarFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  repositoryUrl: 'repositoryUrl',
  license: 'license',
  keyTechnologies: 'keyTechnologies',
  useCases: 'useCases',
  status: 'status',
  contributors: 'contributors',
  stars: 'stars',
  forks: 'forks',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EntityDetailsServiceProviderScalarFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  servicesOffered: 'servicesOffered',
  industrySpecializations: 'industrySpecializations',
  companySizeFocus: 'companySizeFocus',
  portfolioUrl: 'portfolioUrl',
  pricingInfo: 'pricingInfo',
  targetAudience: 'targetAudience',
  locationSummary: 'locationSummary',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EntityDetailsInvestorScalarFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  investmentStages: 'investmentStages',
  focusAreas: 'focusAreas',
  portfolioSize: 'portfolioSize',
  notableInvestments: 'notableInvestments',
  contactEmail: 'contactEmail',
  applicationUrl: 'applicationUrl',
  locationSummary: 'locationSummary',
  investorType: 'investorType',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EntityDetailsEventScalarFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  startDate: 'startDate',
  endDate: 'endDate',
  location: 'location',
  isOnline: 'isOnline',
  registrationUrl: 'registrationUrl',
  price: 'price',
  keySpeakers: 'keySpeakers',
  topics: 'topics',
  eventType: 'eventType',
  targetAudience: 'targetAudience',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EntityDetailsJobScalarFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  companyName: 'companyName',
  location: 'location',
  isRemote: 'isRemote',
  salaryMin: 'salaryMin',
  salaryMax: 'salaryMax',
  jobType: 'jobType',
  experienceLevel: 'experienceLevel',
  keyResponsibilities: 'keyResponsibilities',
  requiredSkills: 'requiredSkills',
  applicationUrl: 'applicationUrl',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EntityDetailsGrantScalarFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  funderName: 'funderName',
  amount: 'amount',
  deadline: 'deadline',
  eligibility: 'eligibility',
  applicationUrl: 'applicationUrl',
  focusAreas: 'focusAreas',
  location: 'location',
  grantType: 'grantType',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EntityDetailsBountyScalarFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  amount: 'amount',
  status: 'status',
  deadline: 'deadline',
  platform: 'platform',
  taskDescription: 'taskDescription',
  requiredSkills: 'requiredSkills',
  url: 'url',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EntityDetailsHardwareScalarFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  processor: 'processor',
  memory: 'memory',
  storage: 'storage',
  gpu: 'gpu',
  price: 'price',
  availability: 'availability',
  useCases: 'useCases',
  powerConsumption: 'powerConsumption',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EntityDetailsNewsScalarFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  publicationDate: 'publicationDate',
  sourceName: 'sourceName',
  author: 'author',
  articleUrl: 'articleUrl',
  summary: 'summary',
  tags: 'tags',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EntityDetailsBookScalarFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  author: 'author',
  publisher: 'publisher',
  publicationDate: 'publicationDate',
  isbn: 'isbn',
  pageCount: 'pageCount',
  format: 'format',
  summary: 'summary',
  purchaseUrl: 'purchaseUrl',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EntityDetailsPodcastScalarFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  host: 'host',
  mainTopics: 'mainTopics',
  averageLength: 'averageLength',
  frequency: 'frequency',
  spotifyUrl: 'spotifyUrl',
  applePodcastsUrl: 'applePodcastsUrl',
  googlePodcastsUrl: 'googlePodcastsUrl',
  youtubeUrl: 'youtubeUrl',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EntityDetailsPlatformScalarFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  platformType: 'platformType',
  keyServices: 'keyServices',
  documentationUrl: 'documentationUrl',
  hasApi: 'hasApi',
  hasFreeTier: 'hasFreeTier',
  useCases: 'useCases',
  pricingModel: 'pricingModel',
  priceRange: 'priceRange',
  integrations: 'integrations',
  targetAudience: 'targetAudience',
  deploymentOptions: 'deploymentOptions',
  supportedOs: 'supportedOs',
  mobileSupport: 'mobileSupport',
  apiAccess: 'apiAccess',
  customizationLevel: 'customizationLevel',
  trialAvailable: 'trialAvailable',
  demoAvailable: 'demoAvailable',
  openSource: 'openSource',
  supportChannels: 'supportChannels',
  pricingDetails: 'pricingDetails',
  pricingUrl: 'pricingUrl',
  supportEmail: 'supportEmail',
  hasLiveChat: 'hasLiveChat',
  communityUrl: 'communityUrl',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EntityFeatureScalarFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  featureId: 'featureId',
  assignedBy: 'assignedBy',
  createdAt: 'createdAt'
};

exports.Prisma.FeatureScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  slug: 'slug',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.UserOrderByRelevanceFieldEnum = {
  id: 'id',
  authUserId: 'authUserId',
  username: 'username',
  displayName: 'displayName',
  email: 'email',
  profilePictureUrl: 'profilePictureUrl',
  bio: 'bio'
};

exports.Prisma.EntityTypeOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  slug: 'slug',
  iconUrl: 'iconUrl'
};

exports.Prisma.EntityOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  websiteUrl: 'websiteUrl',
  entityTypeId: 'entityTypeId',
  shortDescription: 'shortDescription',
  description: 'description',
  logoUrl: 'logoUrl',
  documentationUrl: 'documentationUrl',
  contactUrl: 'contactUrl',
  privacyPolicyUrl: 'privacyPolicyUrl',
  submitterId: 'submitterId',
  legacyId: 'legacyId',
  metaTitle: 'metaTitle',
  metaDescription: 'metaDescription',
  scrapedReviewSentimentLabel: 'scrapedReviewSentimentLabel',
  locationSummary: 'locationSummary',
  refLink: 'refLink'
};

exports.Prisma.CategoryOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  slug: 'slug',
  description: 'description',
  iconUrl: 'iconUrl',
  parentId: 'parentId'
};

exports.Prisma.TagOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  slug: 'slug'
};

exports.Prisma.EntityTagOrderByRelevanceFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  tagId: 'tagId',
  assignedBy: 'assignedBy'
};

exports.Prisma.EntityCategoryOrderByRelevanceFieldEnum = {
  entityId: 'entityId',
  categoryId: 'categoryId',
  assignedBy: 'assignedBy'
};

exports.Prisma.ReviewOrderByRelevanceFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  userId: 'userId',
  title: 'title',
  content: 'content',
  moderatorId: 'moderatorId',
  moderationNotes: 'moderationNotes'
};

exports.Prisma.ReviewVoteOrderByRelevanceFieldEnum = {
  id: 'id',
  reviewId: 'reviewId',
  userId: 'userId'
};

exports.Prisma.EntityDetailsToolOrderByRelevanceFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  customizationLevel: 'customizationLevel',
  pricingDetails: 'pricingDetails',
  pricingUrl: 'pricingUrl',
  supportEmail: 'supportEmail',
  communityUrl: 'communityUrl'
};

exports.Prisma.EntityDetailsAgencyOrderByRelevanceFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  portfolioUrl: 'portfolioUrl',
  pricingInfo: 'pricingInfo',
  locationSummary: 'locationSummary'
};

exports.Prisma.EntityDetailsContentCreatorOrderByRelevanceFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  creatorName: 'creatorName',
  primaryPlatform: 'primaryPlatform',
  exampleContentUrl: 'exampleContentUrl'
};

exports.Prisma.EntityDetailsCommunityOrderByRelevanceFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  platform: 'platform',
  rulesUrl: 'rulesUrl',
  inviteUrl: 'inviteUrl',
  mainChannelUrl: 'mainChannelUrl'
};

exports.Prisma.EntityDetailsNewsletterOrderByRelevanceFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  frequency: 'frequency',
  archiveUrl: 'archiveUrl',
  subscribeUrl: 'subscribeUrl',
  authorName: 'authorName'
};

exports.Prisma.EntityDetailsCourseOrderByRelevanceFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  instructorName: 'instructorName',
  durationText: 'durationText',
  prerequisites: 'prerequisites',
  syllabusUrl: 'syllabusUrl'
};

exports.Prisma.UserSavedEntityOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId',
  entityId: 'entityId'
};

exports.Prisma.UserFollowedTagOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId',
  tagId: 'tagId'
};

exports.Prisma.UserFollowedCategoryOrderByRelevanceFieldEnum = {
  userId: 'userId',
  categoryId: 'categoryId'
};

exports.Prisma.UserActivityLogOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId',
  entityId: 'entityId',
  categoryId: 'categoryId',
  tagId: 'tagId',
  reviewId: 'reviewId',
  targetUserId: 'targetUserId'
};

exports.Prisma.UserNotificationSettingsOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId'
};

exports.Prisma.BadgeTypeOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  iconUrl: 'iconUrl'
};

exports.Prisma.UserBadgeOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId',
  badgeTypeId: 'badgeTypeId',
  grantedBy: 'grantedBy',
  notes: 'notes'
};

exports.Prisma.EntityBadgeOrderByRelevanceFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  badgeTypeId: 'badgeTypeId',
  grantedBy: 'grantedBy',
  notes: 'notes'
};

exports.Prisma.EntityDetailsDatasetOrderByRelevanceFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  format: 'format',
  sourceUrl: 'sourceUrl',
  license: 'license',
  description: 'description',
  accessNotes: 'accessNotes',
  collectionMethod: 'collectionMethod',
  updateFrequency: 'updateFrequency'
};

exports.Prisma.EntityDetailsResearchPaperOrderByRelevanceFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  doi: 'doi',
  authors: 'authors',
  journalOrConference: 'journalOrConference',
  abstract: 'abstract',
  pdfUrl: 'pdfUrl'
};

exports.Prisma.EntityDetailsSoftwareOrderByRelevanceFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  currentVersion: 'currentVersion',
  licenseType: 'licenseType',
  keyFeatures: 'keyFeatures',
  useCases: 'useCases',
  integrations: 'integrations',
  platformCompatibility: 'platformCompatibility',
  programmingLanguages: 'programmingLanguages',
  frameworks: 'frameworks',
  libraries: 'libraries',
  targetAudience: 'targetAudience',
  deploymentOptions: 'deploymentOptions',
  supportedOs: 'supportedOs',
  customizationLevel: 'customizationLevel',
  supportChannels: 'supportChannels',
  pricingDetails: 'pricingDetails',
  pricingUrl: 'pricingUrl',
  supportEmail: 'supportEmail',
  communityUrl: 'communityUrl'
};

exports.Prisma.EntityDetailsModelOrderByRelevanceFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  license: 'license',
  modelArchitecture: 'modelArchitecture',
  trainingDataset: 'trainingDataset',
  useCases: 'useCases',
  frameworks: 'frameworks',
  libraries: 'libraries',
  targetAudience: 'targetAudience',
  deploymentOptions: 'deploymentOptions',
  inputDataTypes: 'inputDataTypes',
  outputDataTypes: 'outputDataTypes'
};

exports.Prisma.EntityDetailsProjectReferenceOrderByRelevanceFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  repositoryUrl: 'repositoryUrl',
  license: 'license',
  keyTechnologies: 'keyTechnologies',
  useCases: 'useCases',
  status: 'status'
};

exports.Prisma.EntityDetailsServiceProviderOrderByRelevanceFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  servicesOffered: 'servicesOffered',
  industrySpecializations: 'industrySpecializations',
  companySizeFocus: 'companySizeFocus',
  portfolioUrl: 'portfolioUrl',
  pricingInfo: 'pricingInfo',
  targetAudience: 'targetAudience',
  locationSummary: 'locationSummary'
};

exports.Prisma.EntityDetailsInvestorOrderByRelevanceFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  investmentStages: 'investmentStages',
  focusAreas: 'focusAreas',
  notableInvestments: 'notableInvestments',
  contactEmail: 'contactEmail',
  applicationUrl: 'applicationUrl',
  locationSummary: 'locationSummary',
  investorType: 'investorType'
};

exports.Prisma.EntityDetailsEventOrderByRelevanceFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  location: 'location',
  registrationUrl: 'registrationUrl',
  price: 'price',
  keySpeakers: 'keySpeakers',
  topics: 'topics',
  eventType: 'eventType',
  targetAudience: 'targetAudience'
};

exports.Prisma.EntityDetailsJobOrderByRelevanceFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  companyName: 'companyName',
  location: 'location',
  jobType: 'jobType',
  experienceLevel: 'experienceLevel',
  keyResponsibilities: 'keyResponsibilities',
  requiredSkills: 'requiredSkills',
  applicationUrl: 'applicationUrl'
};

exports.Prisma.EntityDetailsGrantOrderByRelevanceFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  funderName: 'funderName',
  amount: 'amount',
  eligibility: 'eligibility',
  applicationUrl: 'applicationUrl',
  focusAreas: 'focusAreas',
  location: 'location',
  grantType: 'grantType'
};

exports.Prisma.EntityDetailsBountyOrderByRelevanceFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  amount: 'amount',
  status: 'status',
  platform: 'platform',
  taskDescription: 'taskDescription',
  requiredSkills: 'requiredSkills',
  url: 'url'
};

exports.Prisma.EntityDetailsHardwareOrderByRelevanceFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  processor: 'processor',
  memory: 'memory',
  storage: 'storage',
  gpu: 'gpu',
  price: 'price',
  availability: 'availability',
  useCases: 'useCases',
  powerConsumption: 'powerConsumption'
};

exports.Prisma.EntityDetailsNewsOrderByRelevanceFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  sourceName: 'sourceName',
  author: 'author',
  articleUrl: 'articleUrl',
  summary: 'summary',
  tags: 'tags'
};

exports.Prisma.EntityDetailsBookOrderByRelevanceFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  author: 'author',
  publisher: 'publisher',
  isbn: 'isbn',
  format: 'format',
  summary: 'summary',
  purchaseUrl: 'purchaseUrl'
};

exports.Prisma.EntityDetailsPodcastOrderByRelevanceFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  host: 'host',
  mainTopics: 'mainTopics',
  averageLength: 'averageLength',
  frequency: 'frequency',
  spotifyUrl: 'spotifyUrl',
  applePodcastsUrl: 'applePodcastsUrl',
  googlePodcastsUrl: 'googlePodcastsUrl',
  youtubeUrl: 'youtubeUrl'
};

exports.Prisma.EntityDetailsPlatformOrderByRelevanceFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  platformType: 'platformType',
  keyServices: 'keyServices',
  documentationUrl: 'documentationUrl',
  useCases: 'useCases',
  integrations: 'integrations',
  targetAudience: 'targetAudience',
  deploymentOptions: 'deploymentOptions',
  supportedOs: 'supportedOs',
  customizationLevel: 'customizationLevel',
  supportChannels: 'supportChannels',
  pricingDetails: 'pricingDetails',
  pricingUrl: 'pricingUrl',
  supportEmail: 'supportEmail',
  communityUrl: 'communityUrl'
};

exports.Prisma.EntityFeatureOrderByRelevanceFieldEnum = {
  id: 'id',
  entityId: 'entityId',
  featureId: 'featureId',
  assignedBy: 'assignedBy'
};

exports.Prisma.FeatureOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  slug: 'slug'
};
exports.UserRole = exports.$Enums.UserRole = {
  USER: 'USER',
  ADMIN: 'ADMIN',
  MODERATOR: 'MODERATOR'
};

exports.UserStatus = exports.$Enums.UserStatus = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  PENDING: 'PENDING',
  SUSPENDED: 'SUSPENDED',
  DELETED: 'DELETED'
};

exports.TechnicalLevel = exports.$Enums.TechnicalLevel = {
  BEGINNER: 'BEGINNER',
  INTERMEDIATE: 'INTERMEDIATE',
  ADVANCED: 'ADVANCED',
  EXPERT: 'EXPERT'
};

exports.EntityStatus = exports.$Enums.EntityStatus = {
  PENDING: 'PENDING',
  ACTIVE: 'ACTIVE',
  REJECTED: 'REJECTED',
  NEEDS_REVISION: 'NEEDS_REVISION',
  INACTIVE: 'INACTIVE',
  ARCHIVED: 'ARCHIVED'
};

exports.EmployeeCountRange = exports.$Enums.EmployeeCountRange = {
  C1_10: 'C1_10',
  C11_50: 'C11_50',
  C51_200: 'C51_200',
  C201_500: 'C201_500',
  C501_1000: 'C501_1000',
  C1001_5000: 'C1001_5000',
  C5001_PLUS: 'C5001_PLUS'
};

exports.FundingStage = exports.$Enums.FundingStage = {
  SEED: 'SEED',
  PRE_SEED: 'PRE_SEED',
  SERIES_A: 'SERIES_A',
  SERIES_B: 'SERIES_B',
  SERIES_C: 'SERIES_C',
  SERIES_D_PLUS: 'SERIES_D_PLUS',
  PUBLIC: 'PUBLIC'
};

exports.AffiliateStatus = exports.$Enums.AffiliateStatus = {
  NONE: 'NONE',
  APPLIED: 'APPLIED',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED'
};

exports.ReviewStatus = exports.$Enums.ReviewStatus = {
  PENDING: 'PENDING',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED'
};

exports.LearningCurve = exports.$Enums.LearningCurve = {
  LOW: 'LOW',
  MEDIUM: 'MEDIUM',
  HIGH: 'HIGH'
};

exports.PricingModel = exports.$Enums.PricingModel = {
  FREE: 'FREE',
  FREEMIUM: 'FREEMIUM',
  SUBSCRIPTION: 'SUBSCRIPTION',
  PAY_PER_USE: 'PAY_PER_USE',
  ONE_TIME_PURCHASE: 'ONE_TIME_PURCHASE',
  CONTACT_SALES: 'CONTACT_SALES',
  OPEN_SOURCE: 'OPEN_SOURCE'
};

exports.PriceRange = exports.$Enums.PriceRange = {
  FREE: 'FREE',
  LOW: 'LOW',
  MEDIUM: 'MEDIUM',
  HIGH: 'HIGH',
  ENTERPRISE: 'ENTERPRISE'
};

exports.SkillLevel = exports.$Enums.SkillLevel = {
  BEGINNER: 'BEGINNER',
  INTERMEDIATE: 'INTERMEDIATE',
  ADVANCED: 'ADVANCED',
  EXPERT: 'EXPERT'
};

exports.ActionType = exports.$Enums.ActionType = {
  VIEW_ENTITY: 'VIEW_ENTITY',
  CLICK_ENTITY_LINK: 'CLICK_ENTITY_LINK',
  SAVE_ENTITY: 'SAVE_ENTITY',
  UNSAVE_ENTITY: 'UNSAVE_ENTITY',
  SUBMIT_REVIEW: 'SUBMIT_REVIEW',
  VOTE_REVIEW: 'VOTE_REVIEW',
  FOLLOW_TAG: 'FOLLOW_TAG',
  UNFOLLOW_TAG: 'UNFOLLOW_TAG',
  FOLLOW_CATEGORY: 'FOLLOW_CATEGORY',
  UNFOLLOW_CATEGORY: 'UNFOLLOW_CATEGORY',
  SEARCH: 'SEARCH',
  LOGIN: 'LOGIN',
  LOGOUT: 'LOGOUT',
  SIGNUP: 'SIGNUP',
  UPDATE_PROFILE: 'UPDATE_PROFILE',
  GRANT_BADGE: 'GRANT_BADGE',
  REVOKE_BADGE: 'REVOKE_BADGE'
};

exports.BadgeScope = exports.$Enums.BadgeScope = {
  USER: 'USER',
  ENTITY: 'ENTITY'
};

exports.Prisma.ModelName = {
  User: 'User',
  EntityType: 'EntityType',
  Entity: 'Entity',
  Category: 'Category',
  Tag: 'Tag',
  EntityTag: 'EntityTag',
  EntityCategory: 'EntityCategory',
  Review: 'Review',
  ReviewVote: 'ReviewVote',
  EntityDetailsTool: 'EntityDetailsTool',
  EntityDetailsAgency: 'EntityDetailsAgency',
  EntityDetailsContentCreator: 'EntityDetailsContentCreator',
  EntityDetailsCommunity: 'EntityDetailsCommunity',
  EntityDetailsNewsletter: 'EntityDetailsNewsletter',
  EntityDetailsCourse: 'EntityDetailsCourse',
  UserSavedEntity: 'UserSavedEntity',
  UserFollowedTag: 'UserFollowedTag',
  UserFollowedCategory: 'UserFollowedCategory',
  UserActivityLog: 'UserActivityLog',
  UserNotificationSettings: 'UserNotificationSettings',
  BadgeType: 'BadgeType',
  UserBadge: 'UserBadge',
  EntityBadge: 'EntityBadge',
  EntityDetailsDataset: 'EntityDetailsDataset',
  EntityDetailsResearchPaper: 'EntityDetailsResearchPaper',
  EntityDetailsSoftware: 'EntityDetailsSoftware',
  EntityDetailsModel: 'EntityDetailsModel',
  EntityDetailsProjectReference: 'EntityDetailsProjectReference',
  EntityDetailsServiceProvider: 'EntityDetailsServiceProvider',
  EntityDetailsInvestor: 'EntityDetailsInvestor',
  EntityDetailsEvent: 'EntityDetailsEvent',
  EntityDetailsJob: 'EntityDetailsJob',
  EntityDetailsGrant: 'EntityDetailsGrant',
  EntityDetailsBounty: 'EntityDetailsBounty',
  EntityDetailsHardware: 'EntityDetailsHardware',
  EntityDetailsNews: 'EntityDetailsNews',
  EntityDetailsBook: 'EntityDetailsBook',
  EntityDetailsPodcast: 'EntityDetailsPodcast',
  EntityDetailsPlatform: 'EntityDetailsPlatform',
  EntityFeature: 'EntityFeature',
  Feature: 'Feature'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
