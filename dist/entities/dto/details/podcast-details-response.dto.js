"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PodcastDetailsResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class PodcastDetailsResponseDto {
}
exports.PodcastDetailsResponseDto = PodcastDetailsResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Entity ID (UUID) this detail record is associated with.',
        example: 'p6q7r8s9-t0u1-v2w3-x4y5-z6a7b8c9d0e1',
    }),
    __metadata("design:type", String)
], PodcastDetailsResponseDto.prototype, "entityId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The name of the host of the podcast.',
        example: 'John Doe',
    }),
    __metadata("design:type", Object)
], PodcastDetailsResponseDto.prototype, "host", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The main topics of the podcast.',
        type: 'array',
        items: { type: 'string' },
        example: ['AI Ethics', 'ML Research'],
    }),
    __metadata("design:type", Object)
], PodcastDetailsResponseDto.prototype, "topics", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Links to the podcast on various platforms.',
        type: 'object',
        example: { spotify: 'https://spotify.com/podcast', apple: 'https://apple.com/podcast' },
    }),
    __metadata("design:type", Object)
], PodcastDetailsResponseDto.prototype, "platformLinks", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The average length of an episode.',
        example: '45 minutes',
    }),
    __metadata("design:type", Object)
], PodcastDetailsResponseDto.prototype, "avgEpisodeLength", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The frequency of the podcast.',
        example: 'Weekly',
    }),
    __metadata("design:type", Object)
], PodcastDetailsResponseDto.prototype, "frequency", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp of when the podcast details were created',
        example: '2024-12-01T00:00:00.000Z',
    }),
    __metadata("design:type", Date)
], PodcastDetailsResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp of the last update to the podcast details',
        example: '2024-12-10T10:00:00.000Z',
    }),
    __metadata("design:type", Date)
], PodcastDetailsResponseDto.prototype, "updatedAt", void 0);
//# sourceMappingURL=podcast-details-response.dto.js.map