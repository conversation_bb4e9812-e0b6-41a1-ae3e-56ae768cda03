"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EntityListItemResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class EntityTypeMinimalResponseDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'Tool' }),
    __metadata("design:type", String)
], EntityTypeMinimalResponseDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'tool' }),
    __metadata("design:type", String)
], EntityTypeMinimalResponseDto.prototype, "slug", void 0);
class EntityListItemResponseDto {
}
exports.EntityListItemResponseDto = EntityListItemResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Entity ID (UUID)',
        example: '123e4567-e89b-12d3-a456-************',
    }),
    __metadata("design:type", String)
], EntityListItemResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Name of the entity', example: 'Super AI Tool' }),
    __metadata("design:type", String)
], EntityListItemResponseDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: "URL of the entity's logo",
        example: 'https://superaitool.com/logo.png',
    }),
    __metadata("design:type", Object)
], EntityListItemResponseDto.prototype, "logoUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Short description of the entity',
        example: 'A tool that revolutionizes AI development.',
    }),
    __metadata("design:type", Object)
], EntityListItemResponseDto.prototype, "shortDescription", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Website URL of the entity',
        example: 'https://superaitool.com',
    }),
    __metadata("design:type", Object)
], EntityListItemResponseDto.prototype, "websiteUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Type of the entity',
        type: () => EntityTypeMinimalResponseDto,
    }),
    __metadata("design:type", EntityTypeMinimalResponseDto)
], EntityListItemResponseDto.prototype, "entityType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Average rating for the entity',
        example: 4.5,
        default: 0,
        type: 'number',
        format: 'float',
    }),
    __metadata("design:type", Number)
], EntityListItemResponseDto.prototype, "avgRating", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Number of reviews for the entity',
        example: 42,
        default: 0,
        type: 'integer',
    }),
    __metadata("design:type", Number)
], EntityListItemResponseDto.prototype, "reviewCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Number of users who have saved this entity',
        example: 123,
        default: 0,
        type: 'integer',
    }),
    __metadata("design:type", Number)
], EntityListItemResponseDto.prototype, "saveCount", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Indicates if the tool has a free tier (only for tools).',
        example: true,
    }),
    __metadata("design:type", Boolean)
], EntityListItemResponseDto.prototype, "hasFreeTier", void 0);
//# sourceMappingURL=entity-list-item-response.dto.js.map