declare class EntityTypeMinimalResponseDto {
    name: string;
    slug: string;
}
export declare class EntityListItemResponseDto {
    id: string;
    name: string;
    logoUrl?: string | null;
    shortDescription?: string | null;
    websiteUrl?: string | null;
    entityType: EntityTypeMinimalResponseDto;
    avgRating: number;
    reviewCount: number;
    saveCount: number;
    hasFreeTier?: boolean;
}
export {};
