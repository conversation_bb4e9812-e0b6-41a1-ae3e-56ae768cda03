"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var EntitiesService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.EntitiesService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const prisma_1 = require("../../generated/prisma/index.js");
const openai_service_1 = require("../openai/openai.service");
const entityTypeSlugToDetailKey = (slug) => {
    const mapping = {
        'ai-tool': 'tool_details',
        'online-course': 'course_details',
        'agency': 'agency_details',
        'content-creator': 'content_creator_details',
        'community': 'community_details',
        'newsletter': 'newsletter_details',
        'dataset': 'dataset_details',
        'research-paper': 'research_paper_details',
        'software': 'software_details',
        'model': 'model_details',
        'project-reference': 'project_reference_details',
        'service-provider': 'service_provider_details',
        'investor': 'investor_details',
        'event': 'event_details',
        'job': 'job_details',
        'grant': 'grant_details',
        'bounty': 'bounty_details',
        'hardware': 'hardware_details',
        'news': 'news_details',
        'book': 'book_details',
        'podcast': 'podcast_details',
        'platform': 'platform_details',
    };
    return mapping[slug] || null;
};
const entityTypeSlugToPrismaDetailKey = (slug) => {
    const mapping = {
        'ai-tool': 'entityDetailsTool',
        'online-course': 'entityDetailsCourse',
        'agency': 'entityDetailsAgency',
        'content-creator': 'entityDetailsContentCreator',
        'community': 'entityDetailsCommunity',
        'newsletter': 'entityDetailsNewsletter',
        'dataset': 'entityDetailsDataset',
        'research-paper': 'entityDetailsResearchPaper',
        'software': 'entityDetailsSoftware',
        'model': 'entityDetailsModel',
        'project-reference': 'entityDetailsProjectReference',
        'service-provider': 'entityDetailsServiceProvider',
        'investor': 'entityDetailsInvestor',
        'event': 'entityDetailsEvent',
        'job': 'entityDetailsJob',
        'grant': 'entityDetailsGrant',
        'bounty': 'entityDetailsBounty',
        'hardware': 'entityDetailsHardware',
        'news': 'entityDetailsNews',
        'book': 'entityDetailsBook',
        'podcast': 'entityDetailsPodcast',
        'platform': 'entityDetailsPlatform',
    };
    return mapping[slug] || null;
};
let EntitiesService = EntitiesService_1 = class EntitiesService {
    constructor(prisma, openaiService) {
        this.prisma = prisma;
        this.openaiService = openaiService;
        this.entityTypeMap = new Map();
        this.logger = new common_1.Logger(EntitiesService_1.name);
        console.log('----------------------------------------------------');
        console.log('[EntitiesService] CONSTRUCTOR CALLED');
        console.log('----------------------------------------------------');
    }
    async onModuleInit() {
        console.log('----------------------------------------------------');
        console.log('[EntitiesService] ON_MODULE_INIT CALLED. Attempting to load entity types...');
        console.log('----------------------------------------------------');
        await this.loadEntityTypes();
    }
    async loadEntityTypes() {
        console.log('[EntitiesService] loadEntityTypes - STARTING');
        try {
            const entityTypes = await this.prisma.entityType.findMany({
                select: { id: true, slug: true },
            });
            if (entityTypes && entityTypes.length > 0) {
                this.entityTypeMap.clear();
                entityTypes.forEach(type => this.entityTypeMap.set(type.id, type.slug));
                console.log(`[EntitiesService] Entity types loaded into map. Count: ${this.entityTypeMap.size}.`);
                if (this.entityTypeMap.size > 0) {
                    const firstKey = this.entityTypeMap.keys().next().value;
                    if (firstKey) {
                        console.log(`[EntitiesService] Sample map entry - ID: ${firstKey}, Slug: ${this.entityTypeMap.get(firstKey)}`);
                    }
                }
                console.log('[EntitiesService] loadEntityTypes - FINISHED SUCCESSFULLY');
            }
            else {
                console.log('[EntitiesService] No entity types found in the database to load into map.');
            }
        }
        catch (error) {
            console.error('[EntitiesService] loadEntityTypes - CRITICAL FAILURE:', error);
            this.logger.error('[EntitiesService] CRITICAL: Failed to load entity types on startup:', error.stack);
        }
    }
    async generateFtsTextForEntity(entityId, tx) {
        const entityWithDetails = await tx.entity.findUniqueOrThrow({
            where: { id: entityId },
            include: {
                entityType: true,
                entityCategories: { include: { category: true } },
                entityTags: { include: { tag: true } },
                entityFeatures: { include: { feature: true } },
                entityDetailsTool: true,
                entityDetailsCourse: true,
                entityDetailsAgency: true,
                entityDetailsContentCreator: true,
                entityDetailsCommunity: true,
                entityDetailsNewsletter: true,
                entityDetailsDataset: true,
                entityDetailsResearchPaper: true,
                entityDetailsSoftware: true,
                entityDetailsModel: true,
                entityDetailsProjectReference: true,
                entityDetailsServiceProvider: true,
                entityDetailsInvestor: true,
                entityDetailsEvent: true,
                entityDetailsJob: true,
                entityDetailsGrant: true,
                entityDetailsBounty: true,
                entityDetailsHardware: true,
                entityDetailsNews: true,
                entityDetailsBook: true,
                entityDetailsPodcast: true,
                entityDetailsPlatform: true,
            },
        });
        let textToEmbed = `${entityWithDetails.name || ''}. ${entityWithDetails.shortDescription || ''}. ${entityWithDetails.description || ''}.`;
        if (entityWithDetails.entityCategories.length > 0) {
            textToEmbed += ` Categories: ${entityWithDetails.entityCategories
                .map(ec => ec.category.name)
                .join(', ')}.`;
        }
        if (entityWithDetails.entityTags.length > 0) {
            textToEmbed += ` Tags: ${entityWithDetails.entityTags
                .map(et => et.tag.name)
                .join(', ')}.`;
        }
        if (entityWithDetails.entityFeatures.length > 0) {
            textToEmbed += ` Features: ${entityWithDetails.entityFeatures
                .map(ef => ef.feature.name)
                .join(', ')}.`;
        }
        const details = entityWithDetails.entityDetailsTool ||
            entityWithDetails.entityDetailsCourse ||
            entityWithDetails.entityDetailsAgency ||
            entityWithDetails.entityDetailsContentCreator ||
            entityWithDetails.entityDetailsCommunity ||
            entityWithDetails.entityDetailsNewsletter ||
            entityWithDetails.entityDetailsDataset ||
            entityWithDetails.entityDetailsResearchPaper ||
            entityWithDetails.entityDetailsSoftware ||
            entityWithDetails.entityDetailsModel ||
            entityWithDetails.entityDetailsProjectReference ||
            entityWithDetails.entityDetailsServiceProvider ||
            entityWithDetails.entityDetailsInvestor ||
            entityWithDetails.entityDetailsEvent ||
            entityWithDetails.entityDetailsJob ||
            entityWithDetails.entityDetailsGrant ||
            entityWithDetails.entityDetailsBounty ||
            entityWithDetails.entityDetailsHardware ||
            entityWithDetails.entityDetailsNews ||
            entityWithDetails.entityDetailsBook ||
            entityWithDetails.entityDetailsPodcast ||
            entityWithDetails.entityDetailsPlatform;
        if (details) {
            if ('keyFeatures' in details && Array.isArray(details.keyFeatures)) {
                textToEmbed += ` Key Features: ${details.keyFeatures.join(', ')}.`;
            }
            if ('useCases' in details && Array.isArray(details.useCases)) {
                textToEmbed += ` Use Cases: ${details.useCases.join(', ')}.`;
            }
        }
        return textToEmbed;
    }
    async generateAndSaveEmbedding(entityId, tx) {
        this.logger.log(`[Embedding BG] Job Started for entity ${entityId}`);
        try {
            const textToEmbed = await this.generateFtsTextForEntity(entityId, tx);
            this.logger.log(`[Embedding BG] Text prepared for entity ${entityId}: "${textToEmbed.substring(0, 200)}..."`);
            this.logger.debug(`[Embedding BG] Full text for entity ${entityId}: ${textToEmbed}`);
            const embedding = await this.openaiService.generateEmbedding(textToEmbed);
            if (!embedding) {
                this.logger.warn(`[Embedding BG] OpenAI did not return an embedding for entity ${entityId}. Skipping DB update.`);
                return;
            }
            this.logger.log(`[Embedding BG] Embedding generated successfully for entity ${entityId}. Saving to DB...`);
            const vectorString = `[${embedding.join(',')}]`;
            await tx.$executeRaw `UPDATE "public"."entities" SET "vector_embedding" = ${vectorString}::vector, "updated_at" = NOW() WHERE id = ${entityId}::uuid`;
            this.logger.log(`[Embedding BG] Successfully saved embedding for entity ${entityId}.`);
        }
        catch (error) {
            this.logger.error(`[Embedding BG] Job FAILED for entity ${entityId}:`, error.stack);
        }
    }
    mapToolDetailsToPrisma(toolDetails) {
        if (!toolDetails)
            return {};
        const prismaData = {};
        if (toolDetails.learning_curve !== undefined)
            prismaData.learningCurve = toolDetails.learning_curve;
        if (toolDetails.key_features !== undefined)
            prismaData.keyFeatures = toolDetails.key_features;
        if (toolDetails.programming_languages !== undefined)
            prismaData.programmingLanguages = toolDetails.programming_languages;
        if (toolDetails.frameworks !== undefined)
            prismaData.frameworks = toolDetails.frameworks;
        if (toolDetails.libraries !== undefined)
            prismaData.libraries = toolDetails.libraries;
        if (toolDetails.target_audience !== undefined)
            prismaData.targetAudience = toolDetails.target_audience;
        if (toolDetails.deployment_options !== undefined)
            prismaData.deploymentOptions = toolDetails.deployment_options;
        if (toolDetails.supported_os !== undefined)
            prismaData.supportedOs = toolDetails.supported_os;
        if (toolDetails.mobile_support !== undefined)
            prismaData.mobileSupport = toolDetails.mobile_support;
        if (toolDetails.api_access !== undefined)
            prismaData.apiAccess = toolDetails.api_access;
        if (toolDetails.customization_level !== undefined)
            prismaData.customizationLevel = toolDetails.customization_level;
        if (toolDetails.trial_available !== undefined)
            prismaData.trialAvailable = toolDetails.trial_available;
        if (toolDetails.demo_available !== undefined)
            prismaData.demoAvailable = toolDetails.demo_available;
        if (toolDetails.open_source !== undefined)
            prismaData.openSource = toolDetails.open_source;
        if (toolDetails.support_channels !== undefined)
            prismaData.supportChannels = toolDetails.support_channels;
        if (toolDetails.has_free_tier !== undefined)
            prismaData.hasFreeTier = toolDetails.has_free_tier;
        if (toolDetails.use_cases !== undefined)
            prismaData.useCases = toolDetails.use_cases;
        if (toolDetails.integrations !== undefined)
            prismaData.integrations = toolDetails.integrations;
        if (toolDetails.pricing_model !== undefined)
            prismaData.pricingModel = toolDetails.pricing_model;
        if (toolDetails.price_range !== undefined)
            prismaData.priceRange = toolDetails.price_range;
        if (toolDetails.pricing_details !== undefined)
            prismaData.pricingDetails = toolDetails.pricing_details;
        if (toolDetails.pricing_url !== undefined)
            prismaData.pricingUrl = toolDetails.pricing_url;
        if (toolDetails.support_email !== undefined)
            prismaData.supportEmail = toolDetails.support_email;
        if (toolDetails.has_live_chat !== undefined)
            prismaData.hasLiveChat = toolDetails.has_live_chat;
        if (toolDetails.community_url !== undefined)
            prismaData.communityUrl = toolDetails.community_url;
        return prismaData;
    }
    mapCourseDetailsToPrisma(courseDetails) {
        if (!courseDetails)
            return {};
        const prismaData = {};
        if (courseDetails.instructor_name !== undefined)
            prismaData.instructorName = courseDetails.instructor_name;
        if (courseDetails.duration_text !== undefined)
            prismaData.durationText = courseDetails.duration_text;
        if (courseDetails.skill_level !== undefined)
            prismaData.skillLevel = courseDetails.skill_level;
        if (courseDetails.prerequisites !== undefined)
            prismaData.prerequisites = courseDetails.prerequisites;
        if (courseDetails.syllabus_url !== undefined)
            prismaData.syllabusUrl = courseDetails.syllabus_url;
        if (courseDetails.enrollment_count !== undefined)
            prismaData.enrollmentCount = courseDetails.enrollment_count;
        if (courseDetails.certificate_available !== undefined)
            prismaData.certificateAvailable = courseDetails.certificate_available;
        return prismaData;
    }
    mapAgencyDetailsToPrisma(agencyDetails) {
        if (!agencyDetails)
            return {};
        const prismaData = {};
        if (agencyDetails.services_offered !== undefined)
            prismaData.servicesOffered = agencyDetails.services_offered;
        if (agencyDetails.industry_focus !== undefined)
            prismaData.industryFocus = agencyDetails.industry_focus;
        if (agencyDetails.target_client_size !== undefined)
            prismaData.targetClientSize = agencyDetails.target_client_size;
        if (agencyDetails.target_audience !== undefined)
            prismaData.targetAudience = agencyDetails.target_audience;
        if (agencyDetails.location_summary !== undefined)
            prismaData.locationSummary = agencyDetails.location_summary;
        if (agencyDetails.portfolio_url !== undefined)
            prismaData.portfolioUrl = agencyDetails.portfolio_url;
        if (agencyDetails.pricing_info !== undefined)
            prismaData.pricingInfo = agencyDetails.pricing_info;
        return prismaData;
    }
    mapContentCreatorDetailsToPrisma(contentCreatorDetails) {
        if (!contentCreatorDetails)
            return {};
        const prismaData = {};
        if (contentCreatorDetails.creator_name !== undefined)
            prismaData.creatorName = contentCreatorDetails.creator_name;
        if (contentCreatorDetails.primary_platform !== undefined)
            prismaData.primaryPlatform = contentCreatorDetails.primary_platform;
        if (contentCreatorDetails.focus_areas !== undefined)
            prismaData.focusAreas = contentCreatorDetails.focus_areas;
        if (contentCreatorDetails.follower_count !== undefined)
            prismaData.followerCount = contentCreatorDetails.follower_count;
        if (contentCreatorDetails.example_content_url !== undefined)
            prismaData.exampleContentUrl = contentCreatorDetails.example_content_url;
        return prismaData;
    }
    mapCommunityDetailsToPrisma(communityDetails) {
        if (!communityDetails)
            return {};
        const prismaData = {};
        if (communityDetails.platform !== undefined)
            prismaData.platform = communityDetails.platform;
        if (communityDetails.member_count !== undefined)
            prismaData.memberCount = communityDetails.member_count;
        if (communityDetails.focus_topics !== undefined)
            prismaData.focusTopics = communityDetails.focus_topics;
        if (communityDetails.rules_url !== undefined)
            prismaData.rulesUrl = communityDetails.rules_url;
        if (communityDetails.invite_url !== undefined)
            prismaData.inviteUrl = communityDetails.invite_url;
        if (communityDetails.main_channel_url !== undefined)
            prismaData.mainChannelUrl = communityDetails.main_channel_url;
        return prismaData;
    }
    mapNewsletterDetailsToPrisma(newsletterDetails) {
        if (!newsletterDetails)
            return {};
        const prismaData = {};
        if (newsletterDetails.frequency !== undefined)
            prismaData.frequency = newsletterDetails.frequency;
        if (newsletterDetails.main_topics !== undefined)
            prismaData.mainTopics = newsletterDetails.main_topics;
        if (newsletterDetails.archive_url !== undefined)
            prismaData.archiveUrl = newsletterDetails.archive_url;
        if (newsletterDetails.subscribe_url !== undefined)
            prismaData.subscribeUrl = newsletterDetails.subscribe_url;
        if (newsletterDetails.author_name !== undefined)
            prismaData.authorName = newsletterDetails.author_name;
        if (newsletterDetails.subscriber_count !== undefined)
            prismaData.subscriberCount = newsletterDetails.subscriber_count;
        return prismaData;
    }
    mapDetailsToPrisma(detailsDto, entityTypeSlug) {
        switch (entityTypeSlug) {
            case 'ai-tool':
                return this.mapToolDetailsToPrisma(detailsDto);
            case 'online-course':
                return this.mapCourseDetailsToPrisma(detailsDto);
            case 'agency':
                return this.mapAgencyDetailsToPrisma(detailsDto);
            case 'content-creator':
                return this.mapContentCreatorDetailsToPrisma(detailsDto);
            case 'community':
                return this.mapCommunityDetailsToPrisma(detailsDto);
            case 'newsletter':
                return this.mapNewsletterDetailsToPrisma(detailsDto);
            default:
                return detailsDto || {};
        }
    }
    async create(createEntityDto, submitterUser) {
        const { entity_type_id, category_ids, tag_ids, feature_ids, name, website_url, short_description, description, logo_url, documentation_url, contact_url, privacy_policy_url, founded_year, social_links, meta_title, meta_description, employee_count_range, funding_stage, location_summary, ref_link, affiliate_status, scraped_review_sentiment_label, scraped_review_sentiment_score, scraped_review_count, tool_details, course_details, agency_details, content_creator_details, community_details, newsletter_details, dataset_details, research_paper_details, software_details, model_details, project_reference_details, service_provider_details, investor_details, event_details, job_details, grant_details, bounty_details, hardware_details, news_details, book_details, podcast_details, platform_details, } = createEntityDto;
        this.logger.log(`[EntitiesService Create] Received entity_type_id: ${entity_type_id}`);
        this.logger.log(`[EntitiesService Create] Current entityTypeMap (size ${this.entityTypeMap.size}): ${JSON.stringify(Array.from(this.entityTypeMap.entries()))}`);
        const entityTypeSlug = this.entityTypeMap.get(entity_type_id);
        if (!entityTypeSlug) {
            this.logger.error(`[EntitiesService Create] Invalid entity_type_id: ${entity_type_id} not found in map. Current map keys: [${Array.from(this.entityTypeMap.keys()).join(', ')}]`);
            console.error(`[EntitiesService Create] Invalid entity_type_id: ${entity_type_id} not found in map. Current map keys: [${Array.from(this.entityTypeMap.keys())}`);
            throw new common_1.BadRequestException(`Invalid entity_type_id: ${entity_type_id}`);
        }
        const providedDetails = {
            'ai-tool': tool_details,
            'online-course': course_details,
            'agency': agency_details,
            'content-creator': content_creator_details,
            'community': community_details,
            'newsletter': newsletter_details,
            'dataset': dataset_details,
            'research-paper': research_paper_details,
            'software': software_details,
            'model': model_details,
            'project-reference': project_reference_details,
            'service-provider': service_provider_details,
            'investor': investor_details,
            'event': event_details,
            'job': job_details,
            'grant': grant_details,
            'bounty': bounty_details,
            'hardware': hardware_details,
            'news': news_details,
            'book': book_details,
            'podcast': podcast_details,
            'platform': platform_details,
        };
        let expectedDetailKeyForSlug = entityTypeSlugToDetailKey(entityTypeSlug);
        let detailIsProvided = false;
        if (tool_details)
            detailIsProvided = true;
        if (course_details)
            detailIsProvided = true;
        if (agency_details)
            detailIsProvided = true;
        if (content_creator_details)
            detailIsProvided = true;
        if (community_details)
            detailIsProvided = true;
        if (newsletter_details)
            detailIsProvided = true;
        if (dataset_details)
            detailIsProvided = true;
        if (research_paper_details)
            detailIsProvided = true;
        if (software_details)
            detailIsProvided = true;
        if (model_details)
            detailIsProvided = true;
        if (project_reference_details)
            detailIsProvided = true;
        if (service_provider_details)
            detailIsProvided = true;
        if (investor_details)
            detailIsProvided = true;
        if (event_details)
            detailIsProvided = true;
        if (job_details)
            detailIsProvided = true;
        if (grant_details)
            detailIsProvided = true;
        if (bounty_details)
            detailIsProvided = true;
        if (hardware_details)
            detailIsProvided = true;
        if (news_details)
            detailIsProvided = true;
        if (book_details)
            detailIsProvided = true;
        if (podcast_details)
            detailIsProvided = true;
        if (platform_details)
            detailIsProvided = true;
        let correctDetailIsPresent = false;
        if (entityTypeSlug === 'ai-tool' && tool_details)
            correctDetailIsPresent = true;
        else if (entityTypeSlug === 'online-course' && course_details)
            correctDetailIsPresent = true;
        else if (entityTypeSlug === 'agency' && agency_details)
            correctDetailIsPresent = true;
        else if (entityTypeSlug === 'content-creator' && content_creator_details)
            correctDetailIsPresent = true;
        else if (entityTypeSlug === 'community' && community_details)
            correctDetailIsPresent = true;
        else if (entityTypeSlug === 'newsletter' && newsletter_details)
            correctDetailIsPresent = true;
        else if (entityTypeSlug === 'dataset' && dataset_details)
            correctDetailIsPresent = true;
        else if (entityTypeSlug === 'research-paper' && research_paper_details)
            correctDetailIsPresent = true;
        else if (entityTypeSlug === 'software' && software_details)
            correctDetailIsPresent = true;
        else if (entityTypeSlug === 'model' && model_details)
            correctDetailIsPresent = true;
        else if (entityTypeSlug === 'project-reference' && project_reference_details)
            correctDetailIsPresent = true;
        else if (entityTypeSlug === 'service-provider' && service_provider_details)
            correctDetailIsPresent = true;
        else if (entityTypeSlug === 'investor' && investor_details)
            correctDetailIsPresent = true;
        else if (entityTypeSlug === 'event' && event_details)
            correctDetailIsPresent = true;
        else if (entityTypeSlug === 'job' && job_details)
            correctDetailIsPresent = true;
        else if (entityTypeSlug === 'grant' && grant_details)
            correctDetailIsPresent = true;
        else if (entityTypeSlug === 'bounty' && bounty_details)
            correctDetailIsPresent = true;
        else if (entityTypeSlug === 'hardware' && hardware_details)
            correctDetailIsPresent = true;
        else if (entityTypeSlug === 'news' && news_details)
            correctDetailIsPresent = true;
        else if (entityTypeSlug === 'book' && book_details)
            correctDetailIsPresent = true;
        else if (entityTypeSlug === 'podcast' && podcast_details)
            correctDetailIsPresent = true;
        else if (entityTypeSlug === 'platform' && platform_details)
            correctDetailIsPresent = true;
        if (detailIsProvided && !correctDetailIsPresent) {
            throw new common_1.BadRequestException(`Incorrect detail DTO provided for entity type '${entityTypeSlug}'. Expected '${expectedDetailKeyForSlug || entityTypeSlug + '_details'}'.`);
        }
        const actualProvidedDetailObjects = [
            tool_details, course_details, agency_details,
            content_creator_details, community_details, newsletter_details,
            dataset_details, research_paper_details, software_details, model_details,
            project_reference_details, service_provider_details, investor_details,
            event_details, job_details, grant_details, bounty_details, hardware_details,
            news_details, book_details, podcast_details, platform_details,
        ].filter(detail => detail !== undefined);
        if (actualProvidedDetailObjects.length > 1) {
            throw new common_1.BadRequestException('Only one type of entity details can be provided alongside the main entity data.');
        }
        const directEntityData = {
            name,
            websiteUrl: website_url,
            shortDescription: short_description,
            description,
            logoUrl: logo_url,
            documentationUrl: documentation_url,
            contactUrl: contact_url,
            privacyPolicyUrl: privacy_policy_url,
            foundedYear: founded_year,
            socialLinks: social_links,
            metaTitle: meta_title,
            metaDescription: meta_description,
            employeeCountRange: employee_count_range,
            fundingStage: funding_stage,
            locationSummary: location_summary,
            refLink: ref_link,
            affiliateStatus: affiliate_status,
            scrapedReviewSentimentLabel: scraped_review_sentiment_label,
            scrapedReviewSentimentScore: scraped_review_sentiment_score,
            scrapedReviewCount: scraped_review_count,
        };
        const newEntity = await this.prisma.$transaction(async (tx) => {
            const createData = {
                ...directEntityData,
                entityType: { connect: { id: entity_type_id } },
                submitter: { connect: { id: submitterUser.id } },
                status: prisma_1.EntityStatus.PENDING,
                ...(entityTypeSlug === 'ai-tool' && tool_details && { entityDetailsTool: { create: this.mapToolDetailsToPrisma(tool_details) } }),
                ...(entityTypeSlug === 'online-course' && course_details && { entityDetailsCourse: { create: this.mapCourseDetailsToPrisma(course_details) } }),
                ...(entityTypeSlug === 'agency' && agency_details && { entityDetailsAgency: { create: this.mapAgencyDetailsToPrisma(agency_details) } }),
                ...(entityTypeSlug === 'content-creator' && content_creator_details && { entityDetailsContentCreator: { create: this.mapContentCreatorDetailsToPrisma(content_creator_details) } }),
                ...(entityTypeSlug === 'community' && community_details && { entityDetailsCommunity: { create: this.mapCommunityDetailsToPrisma(community_details) } }),
                ...(entityTypeSlug === 'newsletter' && newsletter_details && { entityDetailsNewsletter: { create: this.mapNewsletterDetailsToPrisma(newsletter_details) } }),
                ...(entityTypeSlug === 'dataset' && dataset_details && { entityDetailsDataset: { create: this.mapDetailsToPrisma(dataset_details, entityTypeSlug) } }),
                ...(entityTypeSlug === 'research-paper' && research_paper_details && { entityDetailsResearchPaper: { create: this.mapDetailsToPrisma(research_paper_details, entityTypeSlug) } }),
                ...(entityTypeSlug === 'software' && software_details && { entityDetailsSoftware: { create: this.mapDetailsToPrisma(software_details, entityTypeSlug) } }),
                ...(entityTypeSlug === 'model' && model_details && { entityDetailsModel: { create: this.mapDetailsToPrisma(model_details, entityTypeSlug) } }),
                ...(entityTypeSlug === 'project-reference' && project_reference_details && { entityDetailsProjectReference: { create: this.mapDetailsToPrisma(project_reference_details, entityTypeSlug) } }),
                ...(entityTypeSlug === 'service-provider' && service_provider_details && { entityDetailsServiceProvider: { create: this.mapDetailsToPrisma(service_provider_details, entityTypeSlug) } }),
                ...(entityTypeSlug === 'investor' && investor_details && { entityDetailsInvestor: { create: this.mapDetailsToPrisma(investor_details, entityTypeSlug) } }),
                ...(entityTypeSlug === 'event' && event_details && { entityDetailsEvent: { create: this.mapDetailsToPrisma(event_details, entityTypeSlug) } }),
                ...(entityTypeSlug === 'job' && job_details && { entityDetailsJob: { create: this.mapDetailsToPrisma(job_details, entityTypeSlug) } }),
                ...(entityTypeSlug === 'grant' && grant_details && { entityDetailsGrant: { create: this.mapDetailsToPrisma(grant_details, entityTypeSlug) } }),
                ...(entityTypeSlug === 'bounty' && bounty_details && { entityDetailsBounty: { create: this.mapDetailsToPrisma(bounty_details, entityTypeSlug) } }),
                ...(entityTypeSlug === 'hardware' && hardware_details && { entityDetailsHardware: { create: this.mapDetailsToPrisma(hardware_details, entityTypeSlug) } }),
                ...(entityTypeSlug === 'news' && news_details && { entityDetailsNews: { create: this.mapDetailsToPrisma(news_details, entityTypeSlug) } }),
                ...(entityTypeSlug === 'book' && book_details && { entityDetailsBook: { create: this.mapDetailsToPrisma(book_details, entityTypeSlug) } }),
                ...(entityTypeSlug === 'podcast' && podcast_details && { entityDetailsPodcast: { create: this.mapDetailsToPrisma(podcast_details, entityTypeSlug) } }),
                ...(entityTypeSlug === 'platform' && platform_details && { entityDetailsPlatform: { create: this.mapDetailsToPrisma(platform_details, entityTypeSlug) } }),
                entityCategories: category_ids
                    ? { create: category_ids.map(catId => ({ categoryId: catId, assignedBy: submitterUser.id })) }
                    : undefined,
                entityTags: tag_ids
                    ? { create: tag_ids.map(tagId => ({ tagId: tagId, assignedBy: submitterUser.id })) }
                    : undefined,
                entityFeatures: feature_ids
                    ? { create: feature_ids.map(featId => ({ featureId: featId, assignedBy: submitterUser.id })) }
                    : undefined,
            };
            this.logger.debug(`[EntitiesService Create] createData object: ${JSON.stringify(createData, null, 2)}`);
            const newEntityTx = await tx.entity.create({
                data: createData,
                include: {
                    entityType: true,
                    submitter: {
                        select: {
                            id: true,
                            authUserId: true,
                            email: true,
                            createdAt: true,
                            lastLogin: true,
                            username: true,
                            displayName: true,
                            profilePictureUrl: true
                        }
                    },
                    entityCategories: { include: { category: true } },
                    entityTags: { include: { tag: true } },
                    entityFeatures: { include: { feature: true } },
                    entityDetailsTool: true,
                    entityDetailsCourse: true,
                    entityDetailsAgency: true,
                    entityDetailsContentCreator: true,
                    entityDetailsCommunity: true,
                    entityDetailsNewsletter: true,
                },
            });
            await this.generateAndSaveEmbedding(newEntityTx.id, tx);
            return newEntityTx;
        });
        return newEntity;
    }
    async findAll(listEntitiesDto) {
        const { page = 1, limit = 10, status, entityTypeIds, categoryIds, tagIds, featureIds, searchTerm, sortBy = 'createdAt', sortOrder = 'desc', submitterId, createdAtFrom, createdAtTo, hasFreeTier, employeeCountRanges, fundingStages, locationSearch, apiAccess, pricingModels, priceRanges, integrations, platforms, targetAudience, } = listEntitiesDto;
        const skip = (page - 1) * limit;
        const where = {};
        const detailFilters = [];
        if (status)
            where.status = status;
        if (entityTypeIds?.length)
            where.entityTypeId = { in: entityTypeIds };
        if (submitterId)
            where.submitterId = submitterId;
        if (employeeCountRanges?.length)
            where.employeeCountRange = { in: employeeCountRanges };
        if (fundingStages?.length)
            where.fundingStage = { in: fundingStages };
        if (locationSearch)
            where.locationSummary = { contains: locationSearch, mode: 'insensitive' };
        if (createdAtFrom || createdAtTo) {
            const createdAtCondition = {};
            if (createdAtFrom)
                createdAtCondition.gte = createdAtFrom;
            if (createdAtTo)
                createdAtCondition.lte = createdAtTo;
            where.createdAt = createdAtCondition;
        }
        if (categoryIds?.length)
            where.entityCategories = { some: { categoryId: { in: categoryIds } } };
        if (tagIds?.length)
            where.entityTags = { some: { tagId: { in: tagIds } } };
        if (featureIds?.length)
            where.entityFeatures = { some: { featureId: { in: featureIds } } };
        if (hasFreeTier !== undefined) {
            detailFilters.push({
                OR: [
                    { entityDetailsTool: { hasFreeTier: hasFreeTier } },
                    { entityDetailsSoftware: { hasFreeTier: hasFreeTier } },
                    { entityDetailsPlatform: { hasFreeTier: hasFreeTier } },
                ],
            });
        }
        if (apiAccess !== undefined) {
            detailFilters.push({
                OR: [
                    { entityDetailsTool: { apiAccess: apiAccess } },
                    { entityDetailsSoftware: { apiAccess: apiAccess } },
                    { entityDetailsPlatform: { apiAccess: apiAccess } },
                ],
            });
        }
        if (pricingModels && pricingModels.length > 0) {
            detailFilters.push({
                OR: [
                    { entityDetailsTool: { pricingModel: { in: pricingModels } } },
                    { entityDetailsSoftware: { pricingModel: { in: pricingModels } } },
                    { entityDetailsPlatform: { pricingModel: { in: pricingModels } } },
                ],
            });
        }
        if (priceRanges && priceRanges.length > 0) {
            detailFilters.push({
                OR: [
                    { entityDetailsTool: { priceRange: { in: priceRanges } } },
                    { entityDetailsSoftware: { priceRange: { in: priceRanges } } },
                    { entityDetailsPlatform: { priceRange: { in: priceRanges } } },
                ],
            });
        }
        if (integrations && integrations.length > 0) {
            const query = { array_contains: integrations };
            detailFilters.push({
                OR: [
                    { entityDetailsTool: { integrations: query } },
                    { entityDetailsSoftware: { integrations: query } },
                    { entityDetailsPlatform: { integrations: query } },
                ],
            });
        }
        if (platforms && platforms.length > 0) {
            const query = { array_contains: platforms };
            detailFilters.push({
                OR: [
                    { entityDetailsTool: { supportedOs: query } },
                    { entityDetailsSoftware: { supportedOs: query } },
                    { entityDetailsPlatform: { supportedOs: query } },
                ],
            });
        }
        if (targetAudience && targetAudience.length > 0) {
            const query = { array_contains: targetAudience };
            detailFilters.push({
                OR: [
                    { entityDetailsTool: { targetAudience: query } },
                    { entityDetailsSoftware: { targetAudience: query } },
                    { entityDetailsPlatform: { targetAudience: query } },
                ],
            });
        }
        if (detailFilters.length > 0) {
            where.AND = [...(where.AND || []), ...detailFilters];
        }
        if (searchTerm) {
            const ftsQuery = searchTerm.split(' ').filter(Boolean).join(' & ');
            const count = await this.prisma.entity.count({ where: { ...where, ftsDocument: { search: ftsQuery } } });
            const entities = await this.prisma.entity.findMany({
                where: { ...where, ftsDocument: { search: ftsQuery } },
                include: this.getFindAllIncludes(),
                orderBy: {
                    _relevance: {
                        fields: ['ftsDocument'],
                        search: ftsQuery,
                        sort: 'desc',
                    },
                },
                skip,
                take: limit,
            });
            return {
                data: entities,
                total: count,
                page,
                limit,
                totalPages: Math.ceil(count / limit),
            };
        }
        const total = await this.prisma.entity.count({ where });
        const entities = await this.prisma.entity.findMany({
            where,
            include: this.getFindAllIncludes(),
            orderBy: { [sortBy]: sortOrder },
            skip,
            take: limit,
        });
        return {
            data: entities,
            total,
            page,
            limit,
            totalPages: Math.ceil(total / limit),
        };
    }
    getFindAllIncludes() {
        return {
            entityType: true,
            submitter: {
                select: {
                    id: true,
                    authUserId: true,
                    email: true,
                    createdAt: true,
                    lastLogin: true,
                    username: true,
                    displayName: true,
                    profilePictureUrl: true,
                },
            },
            entityCategories: { include: { category: true } },
            entityTags: { include: { tag: true } },
            entityFeatures: { include: { feature: true } },
            reviews: {
                where: { status: prisma_1.ReviewStatus.APPROVED },
                select: { rating: true },
            },
            entityDetailsTool: true,
            entityDetailsCourse: true,
            entityDetailsAgency: true,
            entityDetailsContentCreator: true,
            entityDetailsCommunity: true,
            entityDetailsNewsletter: true,
            entityDetailsSoftware: true,
            entityDetailsPlatform: true,
            _count: {
                select: {
                    userSavedEntities: true,
                },
            },
        };
    }
    async findOne(id) {
        const entity = await this.prisma.entity.findUnique({
            where: { id },
            include: {
                entityType: true,
                submitter: {
                    select: {
                        id: true,
                        authUserId: true,
                        email: true,
                        createdAt: true,
                        lastLogin: true,
                        username: true,
                        displayName: true,
                        profilePictureUrl: true
                    }
                },
                entityCategories: { include: { category: true } },
                entityTags: { include: { tag: true } },
                entityFeatures: { include: { feature: true } },
                reviews: {
                    where: { status: prisma_1.ReviewStatus.APPROVED },
                    orderBy: { createdAt: 'desc' },
                    include: {
                        user: { select: { id: true, username: true, profilePictureUrl: true } },
                        reviewVotes: true,
                    }
                },
                entityDetailsTool: true,
                entityDetailsCourse: true,
                entityDetailsAgency: true,
                entityDetailsContentCreator: true,
                entityDetailsCommunity: true,
                entityDetailsNewsletter: true,
                entityDetailsDataset: true,
                entityDetailsResearchPaper: true,
                entityDetailsSoftware: true,
                entityDetailsModel: true,
                entityDetailsProjectReference: true,
                entityDetailsServiceProvider: true,
                entityDetailsInvestor: true,
                entityDetailsEvent: true,
                entityDetailsJob: true,
                entityDetailsGrant: true,
                entityDetailsBounty: true,
                entityDetailsHardware: true,
                entityDetailsNews: true,
                entityDetailsBook: true,
                entityDetailsPodcast: true,
                entityDetailsPlatform: true,
            },
        });
        if (!entity) {
            throw new common_1.NotFoundException(`Entity with ID "${id}" not found`);
        }
        return entity;
    }
    async update(id, updateEntityDto, currentUser) {
        const { name, website_url, short_description, description, logo_url, documentation_url, contact_url, privacy_policy_url, founded_year, social_links, category_ids, tag_ids, feature_ids, status, meta_title, meta_description, employee_count_range, funding_stage, location_summary, ref_link, affiliate_status, scraped_review_sentiment_label, scraped_review_sentiment_score, scraped_review_count, tool_details, course_details, agency_details, content_creator_details, community_details, newsletter_details, dataset_details, research_paper_details, software_details, model_details, project_reference_details, service_provider_details, investor_details, event_details, job_details, grant_details, bounty_details, hardware_details, news_details, book_details, podcast_details, platform_details, } = updateEntityDto;
        const entity = await this.prisma.entity.findUnique({
            where: { id },
            include: {
                submitter: true,
                entityCategories: true,
                entityTags: true,
                entityFeatures: true,
                entityType: true,
            },
        });
        if (!entity) {
            throw new common_1.NotFoundException(`Entity with ID "${id}" not found`);
        }
        const canUpdate = entity.submitterId === currentUser.id ||
            currentUser.role === prisma_1.UserRole.ADMIN ||
            currentUser.role === prisma_1.UserRole.MODERATOR;
        if (!canUpdate) {
            throw new common_1.ForbiddenException('You do not have permission to update this entity.');
        }
        const updateData = {};
        if (name !== undefined)
            updateData.name = name;
        if (website_url !== undefined)
            updateData.websiteUrl = website_url;
        if (short_description !== undefined)
            updateData.shortDescription = short_description;
        if (description !== undefined)
            updateData.description = description;
        if (logo_url !== undefined)
            updateData.logoUrl = logo_url;
        if (documentation_url !== undefined)
            updateData.documentationUrl = documentation_url;
        if (contact_url !== undefined)
            updateData.contactUrl = contact_url;
        if (privacy_policy_url !== undefined)
            updateData.privacyPolicyUrl = privacy_policy_url;
        if (founded_year !== undefined)
            updateData.foundedYear = founded_year;
        if (social_links !== undefined)
            updateData.socialLinks = social_links;
        if (meta_title !== undefined)
            updateData.metaTitle = meta_title;
        if (meta_description !== undefined)
            updateData.metaDescription = meta_description;
        if (employee_count_range !== undefined)
            updateData.employeeCountRange = employee_count_range;
        if (funding_stage !== undefined)
            updateData.fundingStage = funding_stage;
        if (location_summary !== undefined)
            updateData.locationSummary = location_summary;
        if (ref_link !== undefined)
            updateData.refLink = ref_link;
        if (affiliate_status !== undefined)
            updateData.affiliateStatus = affiliate_status;
        if (scraped_review_sentiment_label !== undefined)
            updateData.scrapedReviewSentimentLabel = scraped_review_sentiment_label;
        if (scraped_review_sentiment_score !== undefined)
            updateData.scrapedReviewSentimentScore = scraped_review_sentiment_score;
        if (scraped_review_count !== undefined)
            updateData.scrapedReviewCount = scraped_review_count;
        if (status !== undefined) {
            if (currentUser.role === prisma_1.UserRole.ADMIN ||
                currentUser.role === prisma_1.UserRole.MODERATOR) {
                updateData.status = status;
            }
            else if (status === prisma_1.EntityStatus.PENDING && entity.status === prisma_1.EntityStatus.NEEDS_REVISION) {
                updateData.status = prisma_1.EntityStatus.PENDING;
            }
            else if (status !== entity.status) {
                this.logger.warn(`User ${currentUser.id} attempted to change status of entity ${id} from ${entity.status} to ${status} without ADMIN/MODERATOR role.`);
            }
        }
        if (category_ids !== undefined) {
            updateData.entityCategories = {
                deleteMany: {},
                create: category_ids.map(catId => ({ categoryId: catId, assignedBy: currentUser.id })),
            };
        }
        if (tag_ids !== undefined) {
            updateData.entityTags = {
                deleteMany: {},
                create: tag_ids.map(tagId => ({ tagId: tagId, assignedBy: currentUser.id })),
            };
        }
        if (feature_ids !== undefined) {
            updateData.entityFeatures = {
                deleteMany: {},
                create: feature_ids.map(featId => ({ featureId: featId, assignedBy: currentUser.id })),
            };
        }
        const entityTypeSlug = entity.entityType.slug;
        if (entityTypeSlug === 'ai-tool' && tool_details) {
            const detailDataForPrisma = this.mapToolDetailsToPrisma(tool_details);
            if (Object.keys(detailDataForPrisma).length > 0) {
                updateData.entityDetailsTool = {
                    upsert: {
                        create: detailDataForPrisma,
                        update: detailDataForPrisma,
                    },
                };
            }
        }
        else if (entityTypeSlug === 'online-course' && course_details) {
            const detailDataForPrisma = this.mapCourseDetailsToPrisma(course_details);
            if (Object.keys(detailDataForPrisma).length > 0) {
                updateData.entityDetailsCourse = {
                    upsert: {
                        create: detailDataForPrisma,
                        update: detailDataForPrisma,
                    },
                };
            }
        }
        else if (entityTypeSlug === 'agency' && agency_details) {
            const detailDataForPrisma = this.mapAgencyDetailsToPrisma(agency_details);
            if (Object.keys(detailDataForPrisma).length > 0) {
                updateData.entityDetailsAgency = {
                    upsert: {
                        create: detailDataForPrisma,
                        update: detailDataForPrisma,
                    },
                };
            }
        }
        else if (entityTypeSlug === 'content-creator' && content_creator_details) {
            const detailDataForPrisma = this.mapContentCreatorDetailsToPrisma(content_creator_details);
            if (Object.keys(detailDataForPrisma).length > 0) {
                updateData.entityDetailsContentCreator = {
                    upsert: {
                        create: detailDataForPrisma,
                        update: detailDataForPrisma,
                    },
                };
            }
        }
        else if (entityTypeSlug === 'community' && community_details) {
            const detailDataForPrisma = this.mapCommunityDetailsToPrisma(community_details);
            if (Object.keys(detailDataForPrisma).length > 0) {
                updateData.entityDetailsCommunity = {
                    upsert: {
                        create: detailDataForPrisma,
                        update: detailDataForPrisma,
                    },
                };
            }
        }
        else if (entityTypeSlug === 'newsletter' && newsletter_details) {
            const detailDataForPrisma = this.mapNewsletterDetailsToPrisma(newsletter_details);
            if (Object.keys(detailDataForPrisma).length > 0) {
                updateData.entityDetailsNewsletter = {
                    upsert: {
                        create: detailDataForPrisma,
                        update: detailDataForPrisma,
                    },
                };
            }
        }
        else if (entityTypeSlug === 'dataset' && dataset_details) {
            const detailDataForPrisma = this.mapDetailsToPrisma(dataset_details, entityTypeSlug);
            if (Object.keys(detailDataForPrisma).length > 0) {
                updateData.entityDetailsDataset = {
                    upsert: {
                        create: detailDataForPrisma,
                        update: detailDataForPrisma,
                    },
                };
            }
        }
        else if (entityTypeSlug === 'research-paper' && research_paper_details) {
            const detailDataForPrisma = this.mapDetailsToPrisma(research_paper_details, entityTypeSlug);
            if (Object.keys(detailDataForPrisma).length > 0) {
                updateData.entityDetailsResearchPaper = {
                    upsert: {
                        create: detailDataForPrisma,
                        update: detailDataForPrisma,
                    },
                };
            }
        }
        else if (entityTypeSlug === 'software' && software_details) {
            const detailDataForPrisma = this.mapDetailsToPrisma(software_details, entityTypeSlug);
            if (Object.keys(detailDataForPrisma).length > 0) {
                updateData.entityDetailsSoftware = {
                    upsert: {
                        create: detailDataForPrisma,
                        update: detailDataForPrisma,
                    },
                };
            }
        }
        else if (entityTypeSlug === 'model' && model_details) {
            const detailDataForPrisma = this.mapDetailsToPrisma(model_details, entityTypeSlug);
            if (Object.keys(detailDataForPrisma).length > 0) {
                updateData.entityDetailsModel = {
                    upsert: {
                        create: detailDataForPrisma,
                        update: detailDataForPrisma,
                    },
                };
            }
        }
        else if (entityTypeSlug === 'project-reference' && project_reference_details) {
            const detailDataForPrisma = this.mapDetailsToPrisma(project_reference_details, entityTypeSlug);
            if (Object.keys(detailDataForPrisma).length > 0) {
                updateData.entityDetailsProjectReference = {
                    upsert: {
                        create: detailDataForPrisma,
                        update: detailDataForPrisma,
                    },
                };
            }
        }
        else if (entityTypeSlug === 'service-provider' && service_provider_details) {
            const detailDataForPrisma = this.mapDetailsToPrisma(service_provider_details, entityTypeSlug);
            if (Object.keys(detailDataForPrisma).length > 0) {
                updateData.entityDetailsServiceProvider = {
                    upsert: {
                        create: detailDataForPrisma,
                        update: detailDataForPrisma,
                    },
                };
            }
        }
        else if (entityTypeSlug === 'investor' && investor_details) {
            const detailDataForPrisma = this.mapDetailsToPrisma(investor_details, entityTypeSlug);
            if (Object.keys(detailDataForPrisma).length > 0) {
                updateData.entityDetailsInvestor = {
                    upsert: {
                        create: detailDataForPrisma,
                        update: detailDataForPrisma,
                    },
                };
            }
        }
        else if (entityTypeSlug === 'event' && event_details) {
            const detailDataForPrisma = this.mapDetailsToPrisma(event_details, entityTypeSlug);
            if (Object.keys(detailDataForPrisma).length > 0) {
                updateData.entityDetailsEvent = {
                    upsert: {
                        create: detailDataForPrisma,
                        update: detailDataForPrisma,
                    },
                };
            }
        }
        else if (entityTypeSlug === 'job' && job_details) {
            const detailDataForPrisma = this.mapDetailsToPrisma(job_details, entityTypeSlug);
            if (Object.keys(detailDataForPrisma).length > 0) {
                updateData.entityDetailsJob = {
                    upsert: {
                        create: detailDataForPrisma,
                        update: detailDataForPrisma,
                    },
                };
            }
        }
        else if (entityTypeSlug === 'grant' && grant_details) {
            const detailDataForPrisma = this.mapDetailsToPrisma(grant_details, entityTypeSlug);
            if (Object.keys(detailDataForPrisma).length > 0) {
                updateData.entityDetailsGrant = {
                    upsert: {
                        create: detailDataForPrisma,
                        update: detailDataForPrisma,
                    },
                };
            }
        }
        else if (entityTypeSlug === 'bounty' && bounty_details) {
            const detailDataForPrisma = this.mapDetailsToPrisma(bounty_details, entityTypeSlug);
            if (Object.keys(detailDataForPrisma).length > 0) {
                updateData.entityDetailsBounty = {
                    upsert: {
                        create: detailDataForPrisma,
                        update: detailDataForPrisma,
                    },
                };
            }
        }
        else if (entityTypeSlug === 'hardware' && hardware_details) {
            const detailDataForPrisma = this.mapDetailsToPrisma(hardware_details, entityTypeSlug);
            if (Object.keys(detailDataForPrisma).length > 0) {
                updateData.entityDetailsHardware = {
                    upsert: {
                        create: detailDataForPrisma,
                        update: detailDataForPrisma,
                    },
                };
            }
        }
        else if (entityTypeSlug === 'news' && news_details) {
            const detailDataForPrisma = this.mapDetailsToPrisma(news_details, entityTypeSlug);
            if (Object.keys(detailDataForPrisma).length > 0) {
                updateData.entityDetailsNews = {
                    upsert: {
                        create: detailDataForPrisma,
                        update: detailDataForPrisma,
                    },
                };
            }
        }
        else if (entityTypeSlug === 'book' && book_details) {
            const detailDataForPrisma = this.mapDetailsToPrisma(book_details, entityTypeSlug);
            if (Object.keys(detailDataForPrisma).length > 0) {
                updateData.entityDetailsBook = {
                    upsert: {
                        create: detailDataForPrisma,
                        update: detailDataForPrisma,
                    },
                };
            }
        }
        else if (entityTypeSlug === 'podcast' && podcast_details) {
            const detailDataForPrisma = this.mapDetailsToPrisma(podcast_details, entityTypeSlug);
            if (Object.keys(detailDataForPrisma).length > 0) {
                updateData.entityDetailsPodcast = {
                    upsert: {
                        create: detailDataForPrisma,
                        update: detailDataForPrisma,
                    },
                };
            }
        }
        else if (entityTypeSlug === 'platform' && platform_details) {
            const detailDataForPrisma = this.mapDetailsToPrisma(platform_details, entityTypeSlug);
            if (Object.keys(detailDataForPrisma).length > 0) {
                updateData.entityDetailsPlatform = {
                    upsert: {
                        create: detailDataForPrisma,
                        update: detailDataForPrisma,
                    },
                };
            }
        }
        const updatedEntity = await this.prisma.$transaction(async (tx) => {
            const updatedEntityTx = await tx.entity.update({
                where: { id },
                data: updateData,
                include: {
                    entityType: true,
                    submitter: {
                        select: {
                            id: true,
                            authUserId: true,
                            email: true,
                            createdAt: true,
                            lastLogin: true,
                            username: true,
                            displayName: true,
                            profilePictureUrl: true,
                        },
                    },
                    entityCategories: { include: { category: true } },
                    entityTags: { include: { tag: true } },
                    entityFeatures: { include: { feature: true } },
                    entityDetailsTool: true,
                    entityDetailsCourse: true,
                    entityDetailsAgency: true,
                    entityDetailsContentCreator: true,
                    entityDetailsCommunity: true,
                    entityDetailsNewsletter: true,
                    entityDetailsDataset: true,
                    entityDetailsResearchPaper: true,
                    entityDetailsSoftware: true,
                    entityDetailsModel: true,
                    entityDetailsProjectReference: true,
                    entityDetailsServiceProvider: true,
                    entityDetailsInvestor: true,
                    entityDetailsEvent: true,
                    entityDetailsJob: true,
                    entityDetailsGrant: true,
                    entityDetailsBounty: true,
                    entityDetailsHardware: true,
                    entityDetailsNews: true,
                    entityDetailsBook: true,
                    entityDetailsPodcast: true,
                    entityDetailsPlatform: true,
                },
            });
            await this.generateAndSaveEmbedding(updatedEntityTx.id, tx);
            return updatedEntityTx;
        });
        const entityForResponse = await this.prisma.entity.findUniqueOrThrow({
            where: { id: updatedEntity.id },
            include: {
                entityType: true,
                submitter: {
                    select: {
                        id: true,
                        authUserId: true,
                        email: true,
                        createdAt: true,
                        lastLogin: true,
                        username: true,
                        displayName: true,
                        profilePictureUrl: true,
                    },
                },
                entityCategories: { include: { category: true } },
                entityTags: { include: { tag: true } },
                entityFeatures: { include: { feature: true } },
                entityDetailsTool: true,
                entityDetailsCourse: true,
                entityDetailsAgency: true,
                entityDetailsContentCreator: true,
                entityDetailsCommunity: true,
                entityDetailsNewsletter: true,
                entityDetailsDataset: true,
                entityDetailsResearchPaper: true,
                entityDetailsSoftware: true,
                entityDetailsModel: true,
                entityDetailsProjectReference: true,
                entityDetailsServiceProvider: true,
                entityDetailsInvestor: true,
                entityDetailsEvent: true,
                entityDetailsJob: true,
                entityDetailsGrant: true,
                entityDetailsBounty: true,
                entityDetailsHardware: true,
                entityDetailsNews: true,
                entityDetailsBook: true,
                entityDetailsPodcast: true,
                entityDetailsPlatform: true,
            },
        });
        delete entityForResponse.vector_embedding;
        return entityForResponse;
    }
    async adminSetStatus(id, newStatus) {
        const entityExists = await this.prisma.entity.findUnique({
            where: { id },
        });
        if (!entityExists) {
            throw new common_1.NotFoundException(`Entity with ID "${id}" not found`);
        }
        this.logger.log(`[EntitiesService AdminSetStatus] Admin changing status of entity ${id} to ${newStatus}`);
        return this.prisma.entity.update({
            where: { id },
            data: {
                status: newStatus,
            },
            include: {
                entityType: true,
                submitter: {
                    select: {
                        id: true,
                        authUserId: true,
                        email: true,
                        createdAt: true,
                        lastLogin: true,
                        username: true,
                        displayName: true,
                        profilePictureUrl: true
                    }
                },
                entityCategories: { include: { category: true } },
                entityTags: { include: { tag: true } },
                entityDetailsTool: true,
                entityDetailsCourse: true,
                entityDetailsAgency: true,
                entityDetailsContentCreator: true,
                entityDetailsCommunity: true,
                entityDetailsNewsletter: true,
            },
        });
    }
    async remove(id, user) {
        this.logger.log(`[EntitiesService] User ${user.id} requested to remove entity ${id}`);
        const entity = await this.prisma.entity.findUnique({
            where: { id },
        });
        if (!entity) {
            throw new common_1.NotFoundException(`Entity with ID "${id}" not found`);
        }
        const canDelete = entity.submitterId === user.id ||
            user.role === prisma_1.UserRole.ADMIN ||
            user.role === prisma_1.UserRole.MODERATOR;
        if (!canDelete) {
            throw new common_1.ForbiddenException('You do not have permission to delete this entity.');
        }
        await this.prisma.entity.delete({ where: { id } });
    }
    async vectorSearch(vectorSearchDto) {
        const { query, limit } = vectorSearchDto;
        const match_threshold = 0.5;
        const embedding = await this.openaiService.generateEmbedding(query);
        if (!embedding) {
            this.logger.warn(`Could not generate embedding for query: "${query}"`);
            return [];
        }
        const vectorString = `[${embedding.join(',')}]`;
        try {
            const results = await this.prisma.$queryRaw `
        SELECT
          id,
          name,
          "short_description" as "shortDescription",
          "logo_url" as "logoUrl",
          (
            SELECT slug
            FROM "public"."entity_types"
            WHERE id = "entity_type_id"
          ) as "entityTypeSlug",
          1 - ( "vector_embedding" <=> ${vectorString}::vector ) as similarity
        FROM
          "public"."entities"
        WHERE
          1 - ( "vector_embedding" <=> ${vectorString}::vector ) > ${match_threshold}
        ORDER BY
          similarity DESC
        LIMIT ${limit};
      `;
            return results;
        }
        catch (error) {
            this.logger.error('Vector search failed', error.stack);
            throw new common_1.InternalServerErrorException('An error occurred during vector search.');
        }
    }
};
exports.EntitiesService = EntitiesService;
exports.EntitiesService = EntitiesService = EntitiesService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        openai_service_1.OpenaiService])
], EntitiesService);
//# sourceMappingURL=entities.service.js.map