"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReviewResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const _generated_prisma_1 = require("@generated-prisma");
class ReviewUserResponseDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'User ID' }),
    __metadata("design:type", String)
], ReviewUserResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Username' }),
    __metadata("design:type", String)
], ReviewUserResponseDto.prototype, "username", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "User's display name" }),
    __metadata("design:type", String)
], ReviewUserResponseDto.prototype, "displayName", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: "URL of the user's profile picture" }),
    __metadata("design:type", String)
], ReviewUserResponseDto.prototype, "profilePictureUrl", void 0);
class ReviewResponseDto {
}
exports.ReviewResponseDto = ReviewResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Review ID' }),
    __metadata("design:type", String)
], ReviewResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Rating given (1-5)' }),
    __metadata("design:type", Number)
], ReviewResponseDto.prototype, "rating", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Title of the review' }),
    __metadata("design:type", String)
], ReviewResponseDto.prototype, "title", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Main comment of the review' }),
    __metadata("design:type", String)
], ReviewResponseDto.prototype, "comment", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Pros mentioned in the review' }),
    __metadata("design:type", String)
], ReviewResponseDto.prototype, "pros", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Cons mentioned in the review' }),
    __metadata("design:type", String)
], ReviewResponseDto.prototype, "cons", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Status of the review', enum: _generated_prisma_1.ReviewStatus }),
    __metadata("design:type", String)
], ReviewResponseDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Date the review was created' }),
    __metadata("design:type", Date)
], ReviewResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Date the review was last updated' }),
    __metadata("design:type", Date)
], ReviewResponseDto.prototype, "updatedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'ID of the user who submitted the review' }),
    __metadata("design:type", String)
], ReviewResponseDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ type: () => ReviewUserResponseDto, description: 'User who submitted the review' }),
    __metadata("design:type", ReviewUserResponseDto)
], ReviewResponseDto.prototype, "user", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'ID of the entity being reviewed' }),
    __metadata("design:type", String)
], ReviewResponseDto.prototype, "entityId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Number of upvotes for the review' }),
    __metadata("design:type", Number)
], ReviewResponseDto.prototype, "upvotes", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Number of downvotes for the review' }),
    __metadata("design:type", Number)
], ReviewResponseDto.prototype, "downvotes", void 0);
//# sourceMappingURL=review-response.dto.js.map