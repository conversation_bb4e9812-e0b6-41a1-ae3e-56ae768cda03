import { ReviewsService } from './reviews.service';
import { UpdateReviewStatusDto } from './dto/update-review-status.dto';
export declare class AdminReviewsController {
    private readonly reviewsService;
    constructor(reviewsService: ReviewsService);
    updateReviewStatus(id: string, updateReviewStatusDto: UpdateReviewStatusDto, req: any): Promise<{
        title: string | null;
        id: string;
        status: import("../../generated/prisma").$Enums.ReviewStatus;
        createdAt: Date;
        updatedAt: Date;
        content: string | null;
        rating: number;
        moderationNotes: string | null;
        upvotes: number;
        downvotes: number;
        entityId: string;
        moderatorId: string | null;
        userId: string;
    }>;
    adminDeleteReview(id: string, req: any): Promise<void>;
}
