import { TestService } from './test.service';
export declare class TestController {
    private readonly testService;
    constructor(testService: TestService);
    testEntityTypeCrud(): Promise<{
        createReadResult: {
            createdType: {
                description: string | null;
                name: string;
                id: string;
                createdAt: Date;
                updatedAt: Date;
                slug: string;
                iconUrl: string | null;
            };
            readType: {
                description: string | null;
                name: string;
                id: string;
                createdAt: Date;
                updatedAt: Date;
                slug: string;
                iconUrl: string | null;
            } | null;
            allTypesCount: number;
        };
        updateResult: {
            description: string | null;
            name: string;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            slug: string;
            iconUrl: string | null;
        };
        deleteResult: {
            message: string;
            deleted: boolean;
        };
    }>;
    testEntityRelation(): Promise<{
        entityType: {
            description: string | null;
            name: string;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            slug: string;
            iconUrl: string | null;
        };
    } & {
        description: string | null;
        name: string;
        id: string;
        status: import("@generated-prisma").$Enums.EntityStatus;
        socialLinks: import("@generated-prisma/runtime/library").JsonValue | null;
        createdAt: Date;
        updatedAt: Date;
        websiteUrl: string | null;
        shortDescription: string | null;
        logoUrl: string | null;
        documentationUrl: string | null;
        contactUrl: string | null;
        privacyPolicyUrl: string | null;
        foundedYear: number | null;
        legacyId: string | null;
        reviewCount: number;
        avgRating: number;
        metaTitle: string | null;
        metaDescription: string | null;
        scrapedReviewSentimentLabel: string | null;
        scrapedReviewSentimentScore: number | null;
        scrapedReviewCount: number | null;
        employeeCountRange: import("@generated-prisma").$Enums.EmployeeCountRange | null;
        fundingStage: import("@generated-prisma").$Enums.FundingStage | null;
        locationSummary: string | null;
        refLink: string | null;
        affiliateStatus: import("@generated-prisma").$Enums.AffiliateStatus | null;
        entityTypeId: string;
        submitterId: string;
    }>;
    testEntityTagM2MRelation(): Promise<{
        finalEntity: {
            entityTags: ({
                tag: {
                    description: string | null;
                    name: string;
                    id: string;
                    createdAt: Date;
                    updatedAt: Date;
                    slug: string;
                };
            } & {
                id: string;
                createdAt: Date;
                entityId: string;
                tagId: string;
                assignedBy: string;
            })[];
        } & {
            description: string | null;
            name: string;
            id: string;
            status: import("@generated-prisma").$Enums.EntityStatus;
            socialLinks: import("@generated-prisma/runtime/library").JsonValue | null;
            createdAt: Date;
            updatedAt: Date;
            websiteUrl: string | null;
            shortDescription: string | null;
            logoUrl: string | null;
            documentationUrl: string | null;
            contactUrl: string | null;
            privacyPolicyUrl: string | null;
            foundedYear: number | null;
            legacyId: string | null;
            reviewCount: number;
            avgRating: number;
            metaTitle: string | null;
            metaDescription: string | null;
            scrapedReviewSentimentLabel: string | null;
            scrapedReviewSentimentScore: number | null;
            scrapedReviewCount: number | null;
            employeeCountRange: import("@generated-prisma").$Enums.EmployeeCountRange | null;
            fundingStage: import("@generated-prisma").$Enums.FundingStage | null;
            locationSummary: string | null;
            refLink: string | null;
            affiliateStatus: import("@generated-prisma").$Enums.AffiliateStatus | null;
            entityTypeId: string;
            submitterId: string;
        };
        tagSlugs: string[];
    }>;
    testEnumConstraints(): Promise<{
        message: string;
    }>;
    testUniqueConstraints(): Promise<{
        message: string;
    }>;
    testOptionalDefaultsAndTimestamps(): Promise<{
        message: string;
        initial: {
            description: string | null;
            name: string;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            slug: string;
            iconUrl: string | null;
        };
        updated: {
            description: string | null;
            name: string;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            slug: string;
            iconUrl: string | null;
        };
    }>;
}
