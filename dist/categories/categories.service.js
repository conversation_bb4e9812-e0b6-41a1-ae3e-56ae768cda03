"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var CategoriesService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CategoriesService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const prisma_1 = require("generated/prisma");
const slug_utils_1 = require("../utils/slug.utils");
const common_2 = require("@nestjs/common");
let CategoriesService = CategoriesService_1 = class CategoriesService {
    constructor(prisma) {
        this.prisma = prisma;
        this.logger = new common_2.Logger(CategoriesService_1.name);
    }
    async create(createCategoryDto) {
        const slug = createCategoryDto.slug || (0, slug_utils_1.generateSlug)(createCategoryDto.name);
        try {
            const category = await this.prisma.category.create({
                data: {
                    name: createCategoryDto.name,
                    slug: slug,
                    description: createCategoryDto.description,
                    iconUrl: createCategoryDto.icon_url,
                    ...(createCategoryDto.parent_category_id && {
                        parentCategory: {
                            connect: { id: createCategoryDto.parent_category_id },
                        },
                    }),
                },
            });
            return category;
        }
        catch (error) {
            this.logger.error(`Caught error during CategoriesService.create. Error type: ${error?.constructor?.name}, Name: ${error?.name}, Message: ${error?.message}`);
            if (error && typeof error === 'object') {
                if ('code' in error && typeof error.code === 'string' && error.code.startsWith('P')) {
                    this.logger.warn(`Re-throwing PrismaClientKnownRequestError from CategoriesService (Code: ${error.code}) for global filter. Target: ${error.meta?.target}`);
                    throw error;
                }
                else if (error.name === 'PrismaClientValidationError') {
                    this.logger.warn(`Re-throwing PrismaClientValidationError from CategoriesService for global filter.`);
                    throw error;
                }
            }
            this.logger.error(`Unexpected error in CategoriesService.create for category '${createCategoryDto.name}'. Not identified as Prisma error for re-throw. Original error: ${error?.message}`, error?.stack);
            throw new common_1.InternalServerErrorException('Could not create category due to an unexpected server issue.');
        }
    }
    async findAll(page = 1, limit = 10) {
        const skip = (page - 1) * limit;
        try {
            const [categories, totalCount] = await this.prisma.$transaction([
                this.prisma.category.findMany({
                    skip,
                    take: limit,
                    orderBy: {
                        name: 'asc',
                    },
                }),
                this.prisma.category.count(),
            ]);
            return {
                data: categories,
                count: totalCount,
                totalPages: Math.ceil(totalCount / limit),
                currentPage: page,
            };
        }
        catch (error) {
            console.error('Error fetching all categories:', error);
            throw new common_1.InternalServerErrorException('Could not fetch categories.');
        }
    }
    async findAllPublic() {
        try {
            return await this.prisma.category.findMany({
                orderBy: {
                    name: 'asc',
                },
            });
        }
        catch (error) {
            this.logger.error('Error fetching all public categories:', error.stack);
            throw new common_1.InternalServerErrorException('Could not fetch public categories.');
        }
    }
    async findOne(id) {
        const category = await this.prisma.category.findUnique({
            where: { id },
        });
        if (!category) {
            throw new common_1.NotFoundException(`Category with ID "${id}" not found`);
        }
        return category;
    }
    async update(id, updateCategoryDto) {
        await this.findOne(id);
        const dataToUpdate = {};
        if (updateCategoryDto.name !== undefined)
            dataToUpdate.name = updateCategoryDto.name;
        if (updateCategoryDto.description !== undefined)
            dataToUpdate.description = updateCategoryDto.description;
        if (updateCategoryDto.icon_url !== undefined)
            dataToUpdate.iconUrl = updateCategoryDto.icon_url;
        if (updateCategoryDto.hasOwnProperty('parent_category_id')) {
            if (updateCategoryDto.parent_category_id === null) {
                dataToUpdate.parentCategory = { disconnect: true };
            }
            else if (updateCategoryDto.parent_category_id) {
                dataToUpdate.parentCategory = { connect: { id: updateCategoryDto.parent_category_id } };
            }
        }
        if (updateCategoryDto.slug) {
            dataToUpdate.slug = (0, slug_utils_1.generateSlug)(updateCategoryDto.slug);
        }
        else if (updateCategoryDto.name) {
            dataToUpdate.slug = (0, slug_utils_1.generateSlug)(updateCategoryDto.name);
        }
        try {
            return await this.prisma.category.update({
                where: { id },
                data: dataToUpdate,
            });
        }
        catch (error) {
            if (error instanceof prisma_1.Prisma.PrismaClientKnownRequestError) {
                if (error.code === 'P2002') {
                    throw new common_1.ConflictException(`Category with this name or slug already exists.`);
                }
                if (error.code === 'P2025') {
                    throw new common_1.NotFoundException(`Category with ID "${id}" not found to update.`);
                }
            }
            console.error(`Error updating category ${id}:`, error);
            throw new common_1.InternalServerErrorException('Could not update category.');
        }
    }
    async remove(id) {
        await this.findOne(id);
        try {
            const subcategoriesCount = await this.prisma.category.count({
                where: { parentCategoryId: id },
            });
            if (subcategoriesCount > 0) {
                throw new common_1.ConflictException(`Category with ID "${id}" has subcategories. Please delete or reassign them first.`);
            }
            return await this.prisma.category.delete({
                where: { id },
            });
        }
        catch (error) {
            if (error instanceof prisma_1.Prisma.PrismaClientKnownRequestError) {
                if (error.code === 'P2025') {
                    throw new common_1.NotFoundException(`Category with ID "${id}" not found to delete.`);
                }
            }
            if (error instanceof common_1.ConflictException)
                throw error;
            console.error(`Error removing category ${id}:`, error);
            throw new common_1.InternalServerErrorException('Could not delete category.');
        }
    }
};
exports.CategoriesService = CategoriesService;
exports.CategoriesService = CategoriesService = CategoriesService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], CategoriesService);
//# sourceMappingURL=categories.service.js.map