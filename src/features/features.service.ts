import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateFeatureDto } from './dto/create-feature.dto';
import { UpdateFeatureDto } from './dto/update-feature.dto';
import { Prisma, Feature } from '@generated-prisma';
import { generateSlug } from '../utils/slug.utils';

@Injectable()
export class FeaturesService {
  constructor(private readonly prisma: PrismaService) {}

  private async getUniqueSlug(name: string): Promise<string> {
    let slug = generateSlug(name);
    let count = 0;
    while (await this.prisma.feature.findUnique({ where: { slug } })) {
      count++;
      slug = `${generateSlug(name)}-${count}`;
    }
    return slug;
  }

  async create(createFeatureDto: CreateFeatureDto): Promise<Feature> {
    const { name, description, iconUrl } = createFeatureDto;
    const slug = await this.getUniqueSlug(name);

    return this.prisma.feature.create({
      data: {
        name,
        slug,
        description,
        ...(iconUrl && { iconUrl }), // Only include iconUrl if it exists and the column is available
      },
    });
  }

  async findAll(): Promise<Feature[]> {
    return this.prisma.feature.findMany({
      orderBy: {
        name: 'asc',
      },
    });
  }

  async findOne(id: string): Promise<Feature> {
    const feature = await this.prisma.feature.findUnique({
      where: { id },
    });
    if (!feature) {
      throw new NotFoundException(`Feature with ID ${id} not found`);
    }
    return feature;
  }

  async findBySlug(slug: string): Promise<Feature> {
    const feature = await this.prisma.feature.findUnique({
      where: { slug },
    });
    if (!feature) {
      throw new NotFoundException(`Feature with slug ${slug} not found`);
    }
    return feature;
  }

  async update(id: string, updateFeatureDto: UpdateFeatureDto): Promise<Feature> {
    // First, ensure the feature exists.
    await this.findOne(id);
    
    const data: Prisma.FeatureUpdateInput = { ...updateFeatureDto };

    if (updateFeatureDto.name) {
      data.slug = await this.getUniqueSlug(updateFeatureDto.name);
    }

    try {
      return await this.prisma.feature.update({
        where: { id },
        data,
      });
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
        // This is a fallback, but findOne should have already caught it.
        throw new NotFoundException(`Feature with ID ${id} not found`);
      }
      throw error;
    }
  }

  async remove(id: string): Promise<Feature> {
    // Ensure the feature exists before attempting to delete.
    await this.findOne(id);
    
    try {
      return await this.prisma.feature.delete({
        where: { id },
      });
    } catch (error) {
      // This catch block might be redundant if findOne always runs first,
      // but it provides a safety net against race conditions or other issues.
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
        throw new NotFoundException(`Feature with ID ${id} not found`);
      }
      throw error;
    }
  }
} 