import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class PodcastDetailsResponseDto {
  @ApiProperty({
    description: 'Entity ID (UUID) this detail record is associated with.',
    example: 'p6q7r8s9-t0u1-v2w3-x4y5-z6a7b8c9d0e1',
  })
  entityId: string;

  @ApiPropertyOptional({
    description: 'The name of the host of the podcast.',
    example: '<PERSON>',
  })
  host?: string | null;

  @ApiPropertyOptional({
    description: 'The main topics of the podcast.',
    type: 'array',
    items: { type: 'string' },
    example: ['AI Ethics', 'ML Research'],
  })
  topics?: any | null;

  @ApiPropertyOptional({
    description: 'Links to the podcast on various platforms.',
    type: 'object',
    example: { spotify: 'https://spotify.com/podcast', apple: 'https://apple.com/podcast' },
  })
  platformLinks?: any | null;

  @ApiPropertyOptional({
    description: 'The average length of an episode.',
    example: '45 minutes',
  })
  avgEpisodeLength?: string | null;

  @ApiPropertyOptional({
    description: 'The frequency of the podcast.',
    example: 'Weekly',
  })
  frequency?: string | null;

  @ApiProperty({
    description: 'Timestamp of when the podcast details were created',
    example: '2024-12-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Timestamp of the last update to the podcast details',
    example: '2024-12-10T10:00:00.000Z',
  })
  updatedAt: Date;
} 