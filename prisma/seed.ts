import { PrismaClient, Prisma, EntityStatus, TechnicalLevel, PricingModel, SkillLevel, UserRole, UserStatus } from '../generated/prisma';
import type { EntityType, Category, Tag, Feature } from '../generated/prisma';
import crypto from 'crypto';

const prisma = new PrismaClient();

async function main() {
  console.log(`Start seeding ...`);

  // --- Upsert a default submitter user ---
  const defaultUserEmail = '<EMAIL>';
  const defaultAuthUserId = '00000000-0000-0000-0000-000000000000'; // A stable, placeholder UUID for the seed user

  const submitter = await prisma.user.upsert({
    where: { email: defaultUserEmail },
    update: {},
    create: {
      id: crypto.randomUUID(), // Prisma needs an ID here for create
      authUserId: defaultAuthUserId, // This will be the Supabase Auth User ID
      email: defaultUserEmail,
      displayName: 'Seed Submitter',
      role: UserRole.ADMIN, // Give the seed submitter ADMIN role for flexibility
      status: UserStatus.ACTIVE,
    },
  });
  console.log(`Upserted submitter: ${submitter.email} (ID: ${submitter.id}, AuthUID: ${submitter.authUserId})`);

  // --- Seed Entity Types ---
  console.log('Seeding EntityTypes...');
  const entityTypesData = [
    { id: crypto.randomUUID(), name: 'AI Tool', slug: 'ai-tool', description: 'Software, SaaS, APIs, Models, Libraries, Frameworks for AI.' },
    { id: crypto.randomUUID(), name: 'Course', slug: 'course', description: 'Educational courses, tutorials, and learning resources.' },
    { id: crypto.randomUUID(), name: 'Dataset', slug: 'dataset', description: 'Collections of data for training or analysis.' },
    { id: crypto.randomUUID(), name: 'Research Paper', slug: 'research-paper', description: 'Academic papers and research articles.' },
    { id: crypto.randomUUID(), name: 'Platform', slug: 'platform', description: 'Platforms providing AI services or infrastructure.' },
    { id: crypto.randomUUID(), name: 'Hardware', slug: 'hardware', description: 'Physical hardware for AI computation.' },
    { id: crypto.randomUUID(), name: 'Newsletter', slug: 'newsletter', description: 'Regular publications on AI topics.' },
    { id: crypto.randomUUID(), name: 'Community', slug: 'community', description: 'Online or offline AI communities.' },
    { id: crypto.randomUUID(), name: 'Event', slug: 'event', description: 'Conferences, workshops, and meetups.' },
  ];
  const entityTypes: Record<string, EntityType> = {};
  for (const etData of entityTypesData) {
    const entityType = await prisma.entityType.upsert({
      where: { slug: etData.slug }, // Slug is unique for EntityType
      update: { name: etData.name, description: etData.description },
      create: etData, // id is provided in etData
    });
    entityTypes[etData.slug] = entityType;
    console.log(`Upserted EntityType: ${entityType.name}`);
  }

  // --- Seed Categories ---
  console.log('Seeding Categories...');
  const categoriesData = [
    { id: crypto.randomUUID(), name: 'Developer Tools', slug: 'developer-tools', description: 'AI tools for software development.' },
    { id: crypto.randomUUID(), name: 'Natural Language Processing', slug: 'nlp', description: 'Tools and models for NLP.' },
    { id: crypto.randomUUID(), name: 'Computer Vision', slug: 'computer-vision', description: 'Tools and models for image and video analysis.' },
    { id: crypto.randomUUID(), name: 'Machine Learning Platforms', slug: 'ml-platforms', description: 'Platforms for building and deploying ML models.' },
    { id: crypto.randomUUID(), name: 'Data Science & Analytics', slug: 'data-science-analytics', description: 'Resources for data analysis and visualization.' },
    { id: crypto.randomUUID(), name: 'AI Ethics & Governance', slug: 'ai-ethics-governance', description: 'Resources on responsible AI development.' },
    { id: crypto.randomUUID(), name: 'Robotics & Automation', slug: 'robotics-automation', description: 'AI in robotics and process automation.' },
    { id: crypto.randomUUID(), name: 'AI Education & Learning', slug: 'ai-education', description: 'Courses, tutorials, and learning platforms for AI.' },
    { id: crypto.randomUUID(), name: 'AI Infrastructure', slug: 'ai-infrastructure', description: 'Hardware and cloud solutions for AI.' },
  ];
  const categories: Record<string, Category> = {};
  for (const catData of categoriesData) {
    const category = await prisma.category.upsert({
      where: { slug: catData.slug }, // Slug is unique for Category
      update: { name: catData.name, description: catData.description },
      create: catData, // id is provided in catData
    });
    categories[catData.slug] = category;
    console.log(`Upserted Category: ${category.name}`);
  }

  // --- Seed Tags ---
  console.log('Seeding Tags...');
  const tagsData = [
    { id: crypto.randomUUID(), name: 'API Access', slug: 'api-access', description: 'Provides API access.' },
    { id: crypto.randomUUID(), name: 'Open Source', slug: 'open-source', description: 'Open source projects.' },
    { id: crypto.randomUUID(), name: 'Free Tier', slug: 'free-tier', description: 'Offers a free tier.' },
    { id: crypto.randomUUID(), name: 'LLM', slug: 'llm', description: 'Based on Large Language Models.' },
    { id: crypto.randomUUID(), name: 'Python', slug: 'python', description: 'Related to Python.' },
    { id: crypto.randomUUID(), name: 'JavaScript', slug: 'javascript', description: 'Related to JavaScript.' },
    { id: crypto.randomUUID(), name: 'TensorFlow', slug: 'tensorflow', description: 'Uses TensorFlow.' },
    { id: crypto.randomUUID(), name: 'PyTorch', slug: 'pytorch', description: 'Uses PyTorch.' },
    { id: crypto.randomUUID(), name: 'Tutorial', slug: 'tutorial', description: 'Educational tutorial format.' },
    { id: crypto.randomUUID(), name: 'Generative AI', slug: 'generative-ai', description: 'Focuses on generative AI.' },
    { id: crypto.randomUUID(), name: 'Cloud-Based', slug: 'cloud-based', description: 'Runs on cloud infrastructure.' },
    { id: crypto.randomUUID(), name: 'MLOps', slug: 'mlops', description: 'Related to MLOps practices.' },
    { id: crypto.randomUUID(), name: 'Data Visualization', slug: 'data-visualization', description: 'Tools for visualizing data.' },
    { id: crypto.randomUUID(), name: 'Research', slug: 'research', description: 'Focus on AI research.' },
    { id: crypto.randomUUID(), name: 'Beginner-Friendly', slug: 'beginner-friendly', description: 'Suitable for beginners.' },
  ];
  const tags: Record<string, Tag> = {};
  for (const tagData of tagsData) {
    const tag = await prisma.tag.upsert({
      where: { slug: tagData.slug }, // Slug is unique for Tag
      update: { name: tagData.name, description: tagData.description },
      create: tagData, // id is provided in tagData
    });
    tags[tagData.slug] = tag;
    console.log(`Upserted Tag: ${tag.name}`);
  }

  // --- Seed Features ---
  console.log('Seeding Features...');
  const featuresData = [
    { id: crypto.randomUUID(), name: 'Has API', slug: 'has-api', description: 'Offers a programmable API.' },
    { id: crypto.randomUUID(), name: 'Free Trial Available', slug: 'free-trial', description: 'Provides a free trial period.' },
    { id: crypto.randomUUID(), name: 'GUI Interface', slug: 'gui', description: 'Graphical User Interface available.' },
    { id: crypto.randomUUID(), name: 'CLI Tool', slug: 'cli', description: 'Command Line Interface available.' },
    { id: crypto.randomUUID(), name: 'Detailed Documentation', slug: 'documentation', description: 'Comprehensive documentation provided.' },
    { id: crypto.randomUUID(), name: 'Active Community Support', slug: 'community-support', description: 'Active community support channels.' },
    { id: crypto.randomUUID(), name: 'Scalable Architecture', slug: 'scalable', description: 'Designed for scalability.' },
    { id: crypto.randomUUID(), name: 'Pre-trained Models', slug: 'pre-trained-models', description: 'Offers pre-trained models.' },
  ];
  const features: Record<string, Feature> = {};
  for (const featData of featuresData) {
    const feature = await prisma.feature.upsert({
      where: { slug: featData.slug }, // Slug is unique for Feature
      update: { name: featData.name, description: featData.description },
      create: featData, // id is provided in featData
    });
    features[featData.slug] = feature;
    console.log(`Upserted Feature: ${feature.name}`);
  }

  // --- Seed Entities ---
  console.log('Seeding Entities...');
  if (submitter && Object.keys(entityTypes).length > 0 && Object.keys(categories).length > 0 && Object.keys(tags).length > 0 && Object.keys(features).length > 0) {
    const entitiesToSeed = [
      {
        name: 'CodePal AI',
        websiteUrl: 'https://codepal.example.com',
        shortDescription: 'Your AI-powered coding assistant.',
        description: 'CodePal AI helps developers write better code faster using advanced AI models and integrations.',
        status: EntityStatus.ACTIVE,
        entityTypeId: entityTypes['ai-tool'].id,
        details: {
          entityDetailsTool: {
            technicalLevel: TechnicalLevel.INTERMEDIATE,
            hasApi: true,
            pricingModel: PricingModel.FREEMIUM,
            hasFreeTier: true,
            keyFeatures: ['Real-time code completion', 'AI-powered debugging', 'Code generation'],
            useCases: ['Accelerating software development', 'Improving code quality'],
            platforms: ['VS Code Extension', 'JetBrains IDEs', 'Web App'],
          }
        },
        categoryIds: [categories['developer-tools'].id, categories['nlp'].id],
        tagIds: [tags['api-access'].id, tags['llm'].id, tags['python'].id, tags['free-tier'].id],
        featureIds: [features['has-api'].id, features['documentation'].id, features['gui'].id]
      },
      {
        name: 'MLFlow Advanced Course',
        websiteUrl: 'https://mlcourse.example.com/mlflow',
        shortDescription: 'Master MLFlow for MLOps.',
        description: 'A comprehensive course on using MLFlow for managing the machine learning lifecycle, from experimentation to deployment.',
        status: EntityStatus.ACTIVE,
        entityTypeId: entityTypes['course'].id,
        details: {
          entityDetailsCourse: {
            instructorName: 'Dr. AI Expert',
            skillLevel: SkillLevel.ADVANCED,
            durationText: '8 Weeks',
            certificateAvailable: true,
            prerequisites: 'Basic Python, ML concepts, Docker familiarity',
          }
        },
        categoryIds: [categories['ml-platforms'].id, categories['ai-education'].id],
        tagIds: [tags['tutorial'].id, tags['mlops'].id, tags['python'].id ],
        featureIds: [features['documentation'].id, features['community-support']?.id].filter(Boolean)
      },
      // {
      //   name: 'OpenImages V7 Dataset',
      //   websiteUrl: 'https://storage.googleapis.com/openimages/web/index.html',
      //   shortDescription: 'Large-scale, multi-label image dataset.',
      //   description: 'OpenImages is a dataset of ~9M images annotated with image-level labels, object bounding boxes, object segmentation masks, visual relationships, and localized narratives.',
      //   status: EntityStatus.ACTIVE,
      //   entityTypeId: entityTypes['dataset'].id,
      //   details: {
      //     entityDetailsDataset: {
      //       format: 'Mixed (JPEG, CSV for annotations)',
      //       sourceUrl: 'https://storage.googleapis.com/openimages/web/index.html',
      //       license: 'CC BY 4.0',
      //       sizeInBytes: BigInt(10 * 1024 * 1024 * 1024), // Approx 10TB
      //       description: 'Contains bounding boxes for 600 object classes, plus image-level labels for thousands of classes.'
      //     }
      //   },
      //   categoryIds: [categories['computer-vision'].id, categories['data-science-analytics'].id],
      //   tagIds: [tags['open-source'].id, tags['research']?.id].filter(Boolean),
      //   featureIds: []
      // },
      {
        name: 'Attention Is All You Need',
        websiteUrl: 'https://arxiv.org/abs/1706.03762',
        shortDescription: 'The seminal paper introducing the Transformer model.',
        description: 'This paper proposed the Transformer, a novel network architecture based solely on attention mechanisms, dispensing with recurrence and convolutions entirely.',
        status: EntityStatus.ACTIVE,
        entityTypeId: entityTypes['research-paper'].id,
        details: {
          entityDetailsResearchPaper: {
            publicationDate: new Date('2017-06-12'),
            doi: '10.48550/arXiv.1706.03762',
            authors: ['Ashish Vaswani', 'Noam Shazeer', 'Niki Parmar', 'Jakob Uszkoreit', 'Llion Jones', 'Aidan N. Gomez', 'Łukasz Kaiser', 'Illia Polosukhin'],
            journalOrConference: 'NeurIPS 2017',
            citationCount: 90000
          }
        },
        categoryIds: [categories['nlp'].id, categories['ai-education'].id],
        tagIds: [tags['llm'].id, tags['generative-ai'].id, tags['research'].id],
        featureIds: []
      },
      {
        name: 'TensorFlow Extended (TFX)',
        websiteUrl: 'https://www.tensorflow.org/tfx',
        shortDescription: 'An end-to-end platform for deploying production ML pipelines.',
        description: 'TFX is a Google-production-scale machine learning platform that provides a configuration framework and shared libraries to integrate common components needed to define, launch, and monitor your machine learning system.',
        status: EntityStatus.ACTIVE,
        entityTypeId: entityTypes['platform'].id,
        details: {
          entityDetailsPlatform: {
            platformType: 'MLOps Platform',
            keyServices: ['Data Validation', 'Model Training', 'Model Serving', 'Pipeline Orchestration'],
            documentationUrl: 'https://www.tensorflow.org/tfx/guide',
            pricingModel: PricingModel.OPEN_SOURCE
          }
        },
        categoryIds: [categories['ml-platforms'].id, categories['developer-tools'].id, categories['ai-infrastructure'].id],
        tagIds: [tags['tensorflow'].id, tags['mlops'].id, tags['open-source'].id, tags['cloud-based'].id],
        featureIds: [features['documentation'].id, features['open-source']?.id, features['community-support']?.id, features['scalable']?.id].filter(Boolean)
      },
       {
        name: 'AI Ethics Weekly Newsletter',
        websiteUrl: 'https://aiethicsweekly.example.com',
        shortDescription: 'Curated newsletter on AI ethics and responsible AI.',
        description: 'A weekly roundup of news, research, and discussions on the ethical implications of artificial intelligence and how to build more responsible AI systems.',
        status: EntityStatus.ACTIVE,
        entityTypeId: entityTypes['newsletter'].id,
        details: {
          entityDetailsNewsletter: {
            frequency: 'Weekly',
            mainTopics: ['AI Bias', 'Fairness in ML', 'AI Governance', 'AI Transparency'],
            authorName: 'Ethica AI Group',
            subscriberCount: 15000,
            subscribeUrl: 'https://aiethicsweekly.example.com/subscribe'
          }
        },
        categoryIds: [categories['ai-ethics-governance'].id, categories['ai-education'].id],
        tagIds: [tags['generative-ai']?.id, tags['research']?.id].filter(Boolean),
        featureIds: []
      },
      {
        name: 'AI Hardware Showcase 2024',
        websiteUrl: 'https://aihardwareshow.example.com',
        shortDescription: 'Annual event for AI hardware innovations.',
        description: 'The premier event showcasing the latest in AI chips, servers, and hardware solutions from startups and industry giants.',
        status: EntityStatus.ACTIVE,
        entityTypeId: entityTypes['event'].id,
        details: {
          entityDetailsEvent: {
            startDate: new Date('2024-10-22'),
            endDate: new Date('2024-10-24'),
            location: 'San Francisco, CA',
            isVirtual: false,
            mainTopics: ['AI Accelerators', 'Neuromorphic Computing', 'Edge AI Hardware']
          }
        },
        categoryIds: [categories['ai-infrastructure'].id, categories['robotics-automation'].id],
        tagIds: [tags['cloud-based']?.id, tags['research']?.id].filter(Boolean),
        featureIds: []
      }
    ];

    for (const entityData of entitiesToSeed) {
      const { details, categoryIds, tagIds, featureIds, ...restOfEntityData } = entityData;

      // 1. Create the base entity
      const newEntity = await prisma.entity.create({
        data: {
          ...restOfEntityData,
          submitterId: submitter.id,
        },
      });

      // 2. Create related details
      if (details) {
        if (details.entityDetailsTool) {
          await prisma.entityDetailsTool.create({ data: { ...details.entityDetailsTool, entityId: newEntity.id } });
        }
        if (details.entityDetailsCourse) {
          await prisma.entityDetailsCourse.create({ data: { ...details.entityDetailsCourse, entityId: newEntity.id } });
        }
        if (details.entityDetailsResearchPaper) {
          await prisma.entityDetailsResearchPaper.create({ data: { ...details.entityDetailsResearchPaper, entityId: newEntity.id } });
        }
        if (details.entityDetailsPlatform) {
          await prisma.entityDetailsPlatform.create({ data: { ...details.entityDetailsPlatform, entityId: newEntity.id } });
        }
        if (details.entityDetailsNewsletter) {
          await prisma.entityDetailsNewsletter.create({ data: { ...details.entityDetailsNewsletter, entityId: newEntity.id } });
        }
        if (details.entityDetailsEvent) {
          await prisma.entityDetailsEvent.create({ data: { ...details.entityDetailsEvent, entityId: newEntity.id } });
        }
      }

      // 3. Link categories, tags, features (many-to-many)
      if (categoryIds && categoryIds.length > 0) {
        await prisma.entityCategory.createMany({
          data: categoryIds.map(categoryId => ({ entityId: newEntity.id, categoryId, assignedBy: submitter.id })),
        });
      }
      if (tagIds && tagIds.length > 0) {
        await prisma.entityTag.createMany({
          data: tagIds.map(tagId => ({ entityId: newEntity.id, tagId, assignedBy: submitter.id })),
        });
      }
       if (featureIds && featureIds.length > 0) {
        await prisma.entityFeature.createMany({
          data: featureIds.map(featureId => ({ entityId: newEntity.id, featureId, assignedBy: submitter.id })),
        });
      }

      console.log(`Seeded entity: ${newEntity.name}`);
    }
  } else {
    console.log('Skipping entity seeding due to missing prerequisites (submitter, entity types, etc.).');
  }

  console.log(`Seeding finished.`);
}

main()
  .catch(async (e) => {
    console.error(e);
    await prisma.$disconnect();
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  }); 